<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ChatNMatch Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
        }
        .info {
            margin-top: 20px;
            padding: 15px;
            background-color: #e9f7fe;
            border-left: 4px solid #2196F3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>ChatNMatch Test Page</h1>
        <p>This is a simple test page to verify the worker is functioning correctly.</p>
        
        <div class="info">
            <h2>Worker Information</h2>
            <p>URL: <span id="url"></span></p>
            <p>Time: <span id="time"></span></p>
        </div>
    </div>

    <script>
        // Display the current URL
        document.getElementById('url').textContent = window.location.href;
        
        // Display the current time
        document.getElementById('time').textContent = new Date().toLocaleString();
    </script>
</body>
</html>
