import { createClient } from '@supabase/supabase-js';

// Helper function to create a Supabase client
function getSupabaseClient(env) {
  const supabaseUrl = env.SUPABASE_URL || 'https://dknbfjkoiozntvsaguwh.supabase.co';
  const supabaseKey = env.SUPABASE_ANON_KEY || '';
  return createClient(supabaseUrl, supabaseKey);
}

// Helper function to handle CORS
function handleCors(request) {
  // Make sure the necessary headers are present for this to be a valid pre-flight request
  const corsHeaders = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET,HEAD,POST,PUT,DELETE,OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type,Authorization',
    'Access-Control-Max-Age': '86400',
  };

  // Handle OPTIONS request
  if (request.method === 'OPTIONS') {
    return new Response(null, {
      headers: corsHeaders,
    });
  }

  // Return the headers for other requests
  return corsHeaders;
}

// API handlers
const apiHandlers = {
  // Get user profile
  async 'GET /api/profile'(request, env) {
    const supabase = getSupabaseClient(env);
    const authHeader = request.headers.get('Authorization');

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return new Response(JSON.stringify({ error: 'Unauthorized' }), {
        status: 401,
        headers: { 'Content-Type': 'application/json', ...handleCors(request) }
      });
    }

    const token = authHeader.split(' ')[1];

    // Get user data from Supabase
    const { data: { user }, error: userError } = await supabase.auth.getUser(token);

    if (userError || !user) {
      return new Response(JSON.stringify({ error: 'Invalid token' }), {
        status: 401,
        headers: { 'Content-Type': 'application/json', ...handleCors(request) }
      });
    }

    // Get profile data
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', user.id)
      .single();

    if (profileError) {
      return new Response(JSON.stringify({ error: profileError.message }), {
        status: 500,
        headers: { 'Content-Type': 'application/json', ...handleCors(request) }
      });
    }

    return new Response(JSON.stringify({ profile }), {
      headers: { 'Content-Type': 'application/json', ...handleCors(request) }
    });
  },

  // Update user profile
  async 'POST /api/profile'(request, env) {
    const supabase = getSupabaseClient(env);
    const authHeader = request.headers.get('Authorization');

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return new Response(JSON.stringify({ error: 'Unauthorized' }), {
        status: 401,
        headers: { 'Content-Type': 'application/json', ...handleCors(request) }
      });
    }

    const token = authHeader.split(' ')[1];

    // Get user data from Supabase
    const { data: { user }, error: userError } = await supabase.auth.getUser(token);

    if (userError || !user) {
      return new Response(JSON.stringify({ error: 'Invalid token' }), {
        status: 401,
        headers: { 'Content-Type': 'application/json', ...handleCors(request) }
      });
    }

    // Get profile data from request
    const profileData = await request.json();

    // Update profile
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .update(profileData)
      .eq('id', user.id)
      .select()
      .single();

    if (profileError) {
      return new Response(JSON.stringify({ error: profileError.message }), {
        status: 500,
        headers: { 'Content-Type': 'application/json', ...handleCors(request) }
      });
    }

    return new Response(JSON.stringify({ profile }), {
      headers: { 'Content-Type': 'application/json', ...handleCors(request) }
    });
  },

  // Get potential matches with advanced algorithm
  async 'GET /api/matches'(request, env) {
    const supabase = getSupabaseClient(env);
    const authHeader = request.headers.get('Authorization');

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return new Response(JSON.stringify({ error: 'Unauthorized' }), {
        status: 401,
        headers: { 'Content-Type': 'application/json', ...handleCors(request) }
      });
    }

    const token = authHeader.split(' ')[1];

    // Get user data from Supabase
    const { data: { user }, error: userError } = await supabase.auth.getUser(token);

    if (userError || !user) {
      return new Response(JSON.stringify({ error: 'Invalid token' }), {
        status: 401,
        headers: { 'Content-Type': 'application/json', ...handleCors(request) }
      });
    }

    // Get user profile and preferences
    const { data: userProfile, error: profileError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', user.id)
      .single();

    if (profileError) {
      return new Response(JSON.stringify({ error: profileError.message }), {
        status: 500,
        headers: { 'Content-Type': 'application/json', ...handleCors(request) }
      });
    }

    // Extract user preferences
    const preferences = userProfile.preferences || {};
    const genderPreference = preferences.gender_preference || 'all';
    const minAge = preferences.age_min || 18;
    const maxAge = preferences.age_max || 100;
    // const maxDistance = preferences.distance || 100; // km - will use for future location-based matching

    // Get users who have already been liked by the current user
    const { data: alreadyLiked, error: likedError } = await supabase
      .from('likes')
      .select('liked_user_id')
      .eq('user_id', user.id);

    if (likedError) {
      return new Response(JSON.stringify({ error: likedError.message }), {
        status: 500,
        headers: { 'Content-Type': 'application/json', ...handleCors(request) }
      });
    }

    // Create an array of already liked user IDs
    const likedUserIds = alreadyLiked.map(like => like.liked_user_id);

    // Build the query for potential matches
    let query = supabase
      .from('profiles')
      .select('id, name, bio, birth_date, gender, location, avatar_url, interests, preferences')
      .neq('id', user.id);

    // Filter by gender preference if specified
    if (genderPreference !== 'all') {
      query = query.eq('gender', genderPreference);
    }

    // Exclude already liked profiles
    if (likedUserIds.length > 0) {
      query = query.not('id', 'in', `(${likedUserIds.join(',')})`);
    }

    // Execute the query
    const { data: potentialMatches, error: matchesError } = await query.limit(20);

    if (matchesError) {
      return new Response(JSON.stringify({ error: matchesError.message }), {
        status: 500,
        headers: { 'Content-Type': 'application/json', ...handleCors(request) }
      });
    }

    // Apply additional filtering and scoring
    const today = new Date();
    const scoredMatches = potentialMatches.map(profile => {
      // Calculate age
      let age = 25; // Default age
      if (profile.birth_date) {
        const birthDate = new Date(profile.birth_date);
        age = today.getFullYear() - birthDate.getFullYear();
        const monthDiff = today.getMonth() - birthDate.getMonth();
        if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
          age--;
        }
      }

      // Check if age is within preferences
      if (age < minAge || age > maxAge) {
        return { profile, score: -1 }; // Exclude this profile
      }

      // Calculate match score (0-100)
      let score = 50; // Base score

      // Boost score for shared interests
      if (userProfile.interests && profile.interests) {
        const userInterests = new Set(userProfile.interests);
        const sharedInterests = profile.interests.filter(interest => userInterests.has(interest));
        score += sharedInterests.length * 5; // +5 points per shared interest
      }

      // Boost score for mutual preferences
      const otherPreferences = profile.preferences || {};
      if (otherPreferences.gender_preference === 'all' ||
          otherPreferences.gender_preference === userProfile.gender) {
        score += 10; // +10 points if they're interested in your gender
      }

      // Adjust score based on age match
      const otherMinAge = otherPreferences.age_min || 18;
      const otherMaxAge = otherPreferences.age_max || 100;

      // Calculate user age
      let userAge = 25; // Default age
      if (userProfile.birth_date) {
        const birthDate = new Date(userProfile.birth_date);
        userAge = today.getFullYear() - birthDate.getFullYear();
        const monthDiff = today.getMonth() - birthDate.getMonth();
        if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
          userAge--;
        }
      }

      if (userAge >= otherMinAge && userAge <= otherMaxAge) {
        score += 10; // +10 points if you're within their age preference
      }

      return { profile, score, age };
    });

    // Filter out profiles with negative scores and sort by score
    const filteredMatches = scoredMatches
      .filter(match => match.score >= 0)
      .sort((a, b) => b.score - a.score)
      .map(match => ({
        ...match.profile,
        match_score: match.score,
        age: match.age
      }));

    return new Response(JSON.stringify({ matches: filteredMatches }), {
      headers: { 'Content-Type': 'application/json', ...handleCors(request) }
    });
  },

  // Like a profile
  async 'POST /api/like'(request, env) {
    const supabase = getSupabaseClient(env);
    const authHeader = request.headers.get('Authorization');

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return new Response(JSON.stringify({ error: 'Unauthorized' }), {
        status: 401,
        headers: { 'Content-Type': 'application/json', ...handleCors(request) }
      });
    }

    const token = authHeader.split(' ')[1];

    // Get user data from Supabase
    const { data: { user }, error: userError } = await supabase.auth.getUser(token);

    if (userError || !user) {
      return new Response(JSON.stringify({ error: 'Invalid token' }), {
        status: 401,
        headers: { 'Content-Type': 'application/json', ...handleCors(request) }
      });
    }

    // Get liked profile ID from request
    const { profileId } = await request.json();

    if (!profileId) {
      return new Response(JSON.stringify({ error: 'Profile ID is required' }), {
        status: 400,
        headers: { 'Content-Type': 'application/json', ...handleCors(request) }
      });
    }

    // Record the like
    const { error: likeError } = await supabase
      .from('likes')
      .insert({
        user_id: user.id,
        liked_user_id: profileId,
        created_at: new Date().toISOString()
      })
      .select()
      .single();

    if (likeError) {
      return new Response(JSON.stringify({ error: likeError.message }), {
        status: 500,
        headers: { 'Content-Type': 'application/json', ...handleCors(request) }
      });
    }

    // Check if this is a match (the other person has already liked this user)
    const { data: match, error: matchError } = await supabase
      .from('likes')
      .select('*')
      .eq('user_id', profileId)
      .eq('liked_user_id', user.id)
      .single();

    const isMatch = !matchError && match;

    // If it's a match, create a chat room
    let chatRoom = null;
    if (isMatch) {
      const { data: room, error: roomError } = await supabase
        .from('chat_rooms')
        .insert({
          created_at: new Date().toISOString(),
          participants: [user.id, profileId]
        })
        .select()
        .single();

      if (!roomError) {
        chatRoom = room;
      }
    }

    return new Response(JSON.stringify({
      success: true,
      isMatch,
      chatRoom
    }), {
      headers: { 'Content-Type': 'application/json', ...handleCors(request) }
    });
  },

  // Get chat rooms
  async 'GET /api/chats'(request, env) {
    const supabase = getSupabaseClient(env);
    const authHeader = request.headers.get('Authorization');

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return new Response(JSON.stringify({ error: 'Unauthorized' }), {
        status: 401,
        headers: { 'Content-Type': 'application/json', ...handleCors(request) }
      });
    }

    const token = authHeader.split(' ')[1];

    // Get user data from Supabase
    const { data: { user }, error: userError } = await supabase.auth.getUser(token);

    if (userError || !user) {
      return new Response(JSON.stringify({ error: 'Invalid token' }), {
        status: 401,
        headers: { 'Content-Type': 'application/json', ...handleCors(request) }
      });
    }

    // Get chat rooms for this user
    const { data: chatRooms, error: roomsError } = await supabase
      .from('chat_rooms')
      .select('*, profiles!chat_rooms_participants(*)')
      .contains('participants', [user.id]);

    if (roomsError) {
      return new Response(JSON.stringify({ error: roomsError.message }), {
        status: 500,
        headers: { 'Content-Type': 'application/json', ...handleCors(request) }
      });
    }

    return new Response(JSON.stringify({ chatRooms }), {
      headers: { 'Content-Type': 'application/json', ...handleCors(request) }
    });
  },

  // Get chat messages
  async 'GET /api/chats/:roomId/messages'(request, env, params) {
    const supabase = getSupabaseClient(env);
    const authHeader = request.headers.get('Authorization');
    const roomId = params.roomId;

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return new Response(JSON.stringify({ error: 'Unauthorized' }), {
        status: 401,
        headers: { 'Content-Type': 'application/json', ...handleCors(request) }
      });
    }

    const token = authHeader.split(' ')[1];

    // Get user data from Supabase
    const { data: { user }, error: userError } = await supabase.auth.getUser(token);

    if (userError || !user) {
      return new Response(JSON.stringify({ error: 'Invalid token' }), {
        status: 401,
        headers: { 'Content-Type': 'application/json', ...handleCors(request) }
      });
    }

    // Verify user is part of this chat room
    const { data: room, error: roomError } = await supabase
      .from('chat_rooms')
      .select('*')
      .eq('id', roomId)
      .contains('participants', [user.id])
      .single();

    if (roomError || !room) {
      return new Response(JSON.stringify({ error: 'Chat room not found or access denied' }), {
        status: 404,
        headers: { 'Content-Type': 'application/json', ...handleCors(request) }
      });
    }

    // Get messages for this chat room
    const { data: messages, error: messagesError } = await supabase
      .from('messages')
      .select('*, profiles(*)')
      .eq('chat_room_id', roomId)
      .order('created_at', { ascending: true });

    if (messagesError) {
      return new Response(JSON.stringify({ error: messagesError.message }), {
        status: 500,
        headers: { 'Content-Type': 'application/json', ...handleCors(request) }
      });
    }

    return new Response(JSON.stringify({ messages }), {
      headers: { 'Content-Type': 'application/json', ...handleCors(request) }
    });
  },

  // Send a message
  async 'POST /api/chats/:roomId/messages'(request, env, params) {
    const supabase = getSupabaseClient(env);
    const authHeader = request.headers.get('Authorization');
    const roomId = params.roomId;

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return new Response(JSON.stringify({ error: 'Unauthorized' }), {
        status: 401,
        headers: { 'Content-Type': 'application/json', ...handleCors(request) }
      });
    }

    const token = authHeader.split(' ')[1];

    // Get user data from Supabase
    const { data: { user }, error: userError } = await supabase.auth.getUser(token);

    if (userError || !user) {
      return new Response(JSON.stringify({ error: 'Invalid token' }), {
        status: 401,
        headers: { 'Content-Type': 'application/json', ...handleCors(request) }
      });
    }

    // Verify user is part of this chat room
    const { data: room, error: roomError } = await supabase
      .from('chat_rooms')
      .select('*')
      .eq('id', roomId)
      .contains('participants', [user.id])
      .single();

    if (roomError || !room) {
      return new Response(JSON.stringify({ error: 'Chat room not found or access denied' }), {
        status: 404,
        headers: { 'Content-Type': 'application/json', ...handleCors(request) }
      });
    }

    // Get message content from request
    const { content } = await request.json();

    if (!content) {
      return new Response(JSON.stringify({ error: 'Message content is required' }), {
        status: 400,
        headers: { 'Content-Type': 'application/json', ...handleCors(request) }
      });
    }

    // Insert the message
    const { data: message, error: messageError } = await supabase
      .from('messages')
      .insert({
        chat_room_id: roomId,
        sender_id: user.id,
        content,
        created_at: new Date().toISOString()
      })
      .select()
      .single();

    if (messageError) {
      return new Response(JSON.stringify({ error: messageError.message }), {
        status: 500,
        headers: { 'Content-Type': 'application/json', ...handleCors(request) }
      });
    }

    return new Response(JSON.stringify({ message }), {
      headers: { 'Content-Type': 'application/json', ...handleCors(request) }
    });
  }
};

export default {
  async fetch(request, env) {
    const url = new URL(request.url);

    // Handle environment variable requests
    if (url.pathname === '/env-config.js') {
      // Access secrets from env
      const supabaseUrl = env.SUPABASE_URL || 'https://dknbfjkoiozntvsaguwh.supabase.co';
      const supabaseAnonKey = env.SUPABASE_ANON_KEY || '';

      return new Response(
        `window.SUPABASE_URL = "${supabaseUrl}";
         window.SUPABASE_ANON_KEY = "${supabaseAnonKey}";
        `,
        {
          headers: { 'Content-Type': 'application/javascript' }
        }
      );
    }

    // Handle API requests
    if (url.pathname.startsWith('/api/')) {
      // Parse the path to match the handler
      const path = url.pathname;
      const method = request.method;

      // Extract path parameters
      const pathParts = path.split('/');
      let handlerKey = `${method} ${path}`;
      const params = {};

      // Check for parameterized routes like /api/chats/:roomId/messages
      for (const key in apiHandlers) {
        const handlerPathParts = key.split(' ')[1].split('/');

        if (handlerPathParts.length === pathParts.length) {
          let match = true;
          const extractedParams = {};

          for (let i = 0; i < handlerPathParts.length; i++) {
            if (handlerPathParts[i].startsWith(':')) {
              // This is a parameter
              const paramName = handlerPathParts[i].substring(1);
              extractedParams[paramName] = pathParts[i];
            } else if (handlerPathParts[i] !== pathParts[i]) {
              match = false;
              break;
            }
          }

          if (match && key.startsWith(`${method} `)) {
            handlerKey = key;
            Object.assign(params, extractedParams);
            break;
          }
        }
      }

      // Find and execute the handler
      const handler = apiHandlers[handlerKey];
      if (handler) {
        try {
          return await handler(request, env, params);
        } catch (error) {
          return new Response(JSON.stringify({ error: error.message }), {
            status: 500,
            headers: { 'Content-Type': 'application/json', ...handleCors(request) }
          });
        }
      }

      // If no handler found, return 404
      return new Response(JSON.stringify({ error: 'API endpoint not found' }), {
        status: 404,
        headers: { 'Content-Type': 'application/json', ...handleCors(request) }
      });
    }

    // For all other requests, redirect to the Pages deployment
    return Response.redirect('https://30c27c1a.chatnmatch.pages.dev' + url.pathname, 302);
  }
};
