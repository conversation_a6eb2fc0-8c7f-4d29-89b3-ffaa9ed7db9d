# Configuration optimized for Cloudflare Workers free tier

# Worker name (required)
name = "chatnmatch"

# Main entry point for the worker
main = "_worker.js"
compatibility_date = "2023-05-18"

[site]
bucket = "./dist"

[build]
command = "npm run build"

[miniflare]
kv_persist = true

[env.production]
vars = { ENVIRONMENT = "production" }

[env.staging]
vars = { ENVIRONMENT = "staging" }

# Environment variables
[vars]
# Both SUPABASE_URL and SUPABASE_ANON_KEY are set as secrets
# using wrangler secret put SUPABASE_URL
# and wrangler secret put SUPABASE_ANON_KEY

# KV namespace for static assets
[[kv_namespaces]]
binding = "CHATNMATCH_ASSETS"
id = "992199f59d194c09ad5677168822b10d"
preview_id = "992199f59d194c09ad5677168822b10d"

# Free tier doesn't support custom CPU limits
# The default limit is 10ms CPU time per request
