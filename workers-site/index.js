import { getAssetFromKV, mapRequestToAsset } from '@cloudflare/kv-asset-handler';

// Whether to show detailed error messages
const DEBUG = false;

/**
 * <PERSON>le requests to the Worker
 */
export default {
  async fetch(request, env, ctx) {
    const url = new URL(request.url);

    try {
      // Handle API requests
      if (url.pathname.startsWith('/api/')) {
        return new Response(JSON.stringify({ message: 'API endpoint' }), {
          headers: { 'Content-Type': 'application/json' }
        });
      }

      // Handle environment variable requests
      if (url.pathname === '/env-config.js') {
        // Log the available environment variables and secrets for debugging
        console.log('Available env keys:', Object.keys(env));

        // Try to access the secrets
        let supabaseUrl = '';
        let supabaseAnonKey = '';

        try {
          // Try different ways to access the secrets
          supabaseUrl = env.SUPABASE_URL || env.secret?.SUPABASE_URL || '';
          supabaseAnonKey = env.SUPABASE_ANON_KEY || env.secret?.SUPABASE_ANON_KEY || '';

          console.log('Supabase URL available:', !!supabaseUrl);
          console.log('Supabase Anon Key available:', !!supabaseAnonKey);
        } catch (error) {
          console.error('Error accessing secrets:', error);
        }

        return new Response(
          `window.SUPABASE_URL = "${supabaseUrl}";
           window.SUPABASE_ANON_KEY = "${supabaseAnonKey}";
          `,
          {
            headers: { 'Content-Type': 'application/javascript' }
          }
        );
      }

      // Try to serve the requested asset
      try {
        // Modify the HTML response to include the env-config.js script
        const response = await getAssetFromKV({
          request,
          waitUntil: ctx.waitUntil.bind(ctx),
          ASSET_NAMESPACE: env.CHATNMATCH_ASSETS
        });

        // If this is an HTML response, inject our environment variables script
        const contentType = response.headers.get('content-type') || '';
        if (contentType.includes('text/html')) {
          let html = await response.text();
          const scriptTag = `<script src="/env-config.js"></script>`;

          // Insert the script tag before the first script in the HTML
          const scriptPosition = html.indexOf('<script');
          if (scriptPosition !== -1) {
            html = html.slice(0, scriptPosition) + scriptTag + html.slice(scriptPosition);
          } else {
            // If no script tag is found, insert before closing head tag
            const headClosePosition = html.indexOf('</head>');
            if (headClosePosition !== -1) {
              html = html.slice(0, headClosePosition) + scriptTag + html.slice(headClosePosition);
            }
          }

          return new Response(html, response);
        }

        return response;
      } catch (e) {
        // If the asset is not found and the URL doesn't include a file extension,
        // serve index.html for client-side routing
        if (e.status === 404 && !url.pathname.includes('.')) {
          const indexRequest = new Request(`${url.origin}/index.html`, request);
          const response = await getAssetFromKV({
            request: indexRequest,
            waitUntil: ctx.waitUntil.bind(ctx),
            ASSET_NAMESPACE: env.CHATNMATCH_ASSETS
          });

          // Inject environment variables
          let html = await response.text();
          const scriptTag = `<script src="/env-config.js"></script>`;

          // Insert the script tag before the first script in the HTML
          const scriptPosition = html.indexOf('<script');
          if (scriptPosition !== -1) {
            html = html.slice(0, scriptPosition) + scriptTag + html.slice(scriptPosition);
          } else {
            // If no script tag is found, insert before closing head tag
            const headClosePosition = html.indexOf('</head>');
            if (headClosePosition !== -1) {
              html = html.slice(0, headClosePosition) + scriptTag + html.slice(headClosePosition);
            }
          }

          return new Response(html, response);
        }
        throw e;
      }
    } catch (e) {
      // Log the error
      console.error('Worker error:', e);

      // Return a detailed error in debug mode
      if (DEBUG) {
        return new Response(`Error: ${e.message || e.toString()}`, {
          status: 500,
          headers: { 'Content-Type': 'text/plain' }
        });
      }

      // Try to serve index.html as a fallback
      try {
        const indexRequest = new Request(`${url.origin}/index.html`, request);
        const response = await getAssetFromKV({
          request: indexRequest,
          waitUntil: ctx.waitUntil.bind(ctx),
          ASSET_NAMESPACE: env.CHATNMATCH_ASSETS
        });

        // Inject environment variables
        let html = await response.text();
        const scriptTag = `<script src="/env-config.js"></script>`;

        // Insert the script tag before the first script in the HTML
        const scriptPosition = html.indexOf('<script');
        if (scriptPosition !== -1) {
          html = html.slice(0, scriptPosition) + scriptTag + html.slice(scriptPosition);
        } else {
          // If no script tag is found, insert before closing head tag
          const headClosePosition = html.indexOf('</head>');
          if (headClosePosition !== -1) {
            html = html.slice(0, headClosePosition) + scriptTag + html.slice(headClosePosition);
          }
        }

        return new Response(html, response);
      } catch (indexError) {
        console.error('Index fallback error:', indexError);

        // Try to serve fallback.html if index.html fails
        try {
          return await getAssetFromKV({
            request: new Request(`${url.origin}/fallback.html`, request),
            waitUntil: ctx.waitUntil.bind(ctx),
            ASSET_NAMESPACE: env.CHATNMATCH_ASSETS
          });
        } catch (fallbackError) {
          console.error('Fallback HTML error:', fallbackError);

          // If all else fails, return a generic error
          return new Response('Application Error', {
            status: 500,
            headers: { 'Content-Type': 'text/plain' }
          });
        }
      }
    }
  }
};
