# ChatNMatch - A Modern Dating App

ChatNMatch is a full-stack dating application built with React, TypeScript, and Supabase. It features real-time messaging, profile matching, and a clean, responsive UI.

## How can I edit this code?

There are several ways of editing your application.


**Use your preferred IDE**

If you want to work locally using your own IDE, you can clone this repo and push changes. Pushed changes will also be reflected in Lovable.

The only requirement is having Node.js & npm installed - [install with nvm](https://github.com/nvm-sh/nvm#installing-and-updating)

Follow these steps:

```sh
# Step 1: Clone the repository using the project's Git URL.
git clone <YOUR_GIT_URL>

# Step 2: Navigate to the project directory.
cd <YOUR_PROJECT_NAME>

# Step 3: Install the necessary dependencies.
npm i

# Step 4: Start the development server with auto-reloading and an instant preview.
npm run dev
```

**Edit a file directly in GitHub**

- Navigate to the desired file(s).
- Click the "Edit" button (pencil icon) at the top right of the file view.
- Make your changes and commit the changes.

**Use GitHub Codespaces**

- Navigate to the main page of your repository.
- Click on the "Code" button (green button) near the top right.
- Select the "Codespaces" tab.
- Click on "New codespace" to launch a new Codespace environment.
- Edit files directly within the Codespace and commit and push your changes once you're done.

## What technologies are used for this project?

This project is built with modern web technologies:

- Vite - Fast build tool and development server
- TypeScript - Type-safe JavaScript
- React - UI library
- React Router - Client-side routing
- shadcn-ui - Component library
- Tailwind CSS - Utility-first CSS framework
- Supabase - Backend as a Service (BaaS) with:
  - Authentication
  - Database
  - Storage
  - Realtime subscriptions

## How can I deploy this project?

### Deploy to Cloudflare Workers (Free Tier)

This project is optimized for deployment on Cloudflare Workers free tier, which includes:
- 100,000 requests per day
- Up to 10ms CPU time per request
- Unlimited scripts

Follow these steps to deploy:

1. **Create a Cloudflare account**
   - Sign up at [dash.cloudflare.com](https://dash.cloudflare.com/sign-up)
   - Verify your email address

2. **Install Wrangler CLI**
   - The Wrangler CLI is already installed as a dev dependency
   - You can run it with `npx wrangler` commands

3. **Login to Cloudflare**
   ```bash
   npx wrangler login
   ```
   - This will open a browser window to authenticate with Cloudflare

4. **Set up your Supabase secret key**
   ```bash
   npm run setup:worker
   ```
   - When prompted, enter your Supabase anon key

5. **Deploy to Cloudflare Workers**
   ```bash
   npm run deploy
   ```

6. **Access your deployed app**
   - Your app will be available at `https://chatnmatch.workers.dev`
   - You can also set up a custom domain in the Cloudflare dashboard

## Setting up Supabase

1. Create a Supabase account at [supabase.com](https://supabase.com)
2. Create a new project
3. Set up the following tables in your Supabase database:
   - profiles
   - matches
   - messages
4. Update the Supabase URL and anon key in `src/lib/supabase.ts`

## Features

- User authentication (sign up, login, logout)
- Profile creation and editing
- Match discovery and liking
- Real-time messaging
- Responsive design for mobile and desktop
