#!/bin/bash
set -e

echo "=== Custom Build Script ==="

# Force using npm instead of bun
export PATH="/usr/local/bin:/usr/bin:/bin:$PATH"

# Print environment information
echo "Node version: $(node -v)"
echo "NPM version: $(npm -v)"
echo "Current directory: $(pwd)"
echo "Directory contents: $(ls -la)"

# Remove node_modules if it exists
if [ -d "node_modules" ]; then
  echo "Removing existing node_modules directory"
  rm -rf node_modules
fi

# Remove package-lock.json if it exists
if [ -f "package-lock.json" ]; then
  echo "Removing existing package-lock.json"
  rm -f package-lock.json
fi

# Install dependencies using npm
echo "Installing dependencies with npm"
npm install

# Create dist directory if it doesn't exist
echo "Creating dist directory"
mkdir -p dist

# Copy worker files to dist
echo "Copying worker files to dist"
cp src/worker.js dist/

# Copy _worker.js to dist if it exists
if [ -f "_worker.js" ]; then
  echo "Copying _worker.js to dist"
  cp _worker.js dist/
fi

# Create a simple index.html in dist
echo "Creating index.html in dist"
cat > dist/index.html << 'EOL'
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>ChatNMatch</title>
  </head>
  <body>
    <h1>ChatNMatch is working!</h1>
    <p>This is a simple test page to verify the worker is running correctly.</p>
  </body>
</html>
EOL

# Copy any other necessary files to dist
if [ -f "_routes.json" ]; then
  echo "Copying _routes.json to dist"
  cp _routes.json dist/
fi

echo "Build completed successfully"
echo "Contents of dist directory:"
ls -la dist/
