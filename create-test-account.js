// <PERSON>ript to create a test account for ChatN<PERSON>atch
const { createClient } = require('@supabase/supabase-js');

// Replace these with your actual Supabase URL and service role key
// You can find these in your Supabase dashboard under Project Settings > API
const SUPABASE_URL = 'https://dknbfjkoiozntvsaguwh.supabase.co';
const SUPABASE_SERVICE_KEY = 'YOUR_SUPABASE_SERVICE_ROLE_KEY'; // Use service role key for admin operations

// Create a Supabase client with the service role key
const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY);

async function createTestAccount() {
  try {
    // Create a test user
    const email = '<EMAIL>';
    const password = 'Test123!';
    
    console.log(`Creating test user with email: ${email}`);
    
    // Create user with admin privileges (service role)
    const { data: userData, error: userError } = await supabase.auth.admin.createUser({
      email,
      password,
      email_confirm: true, // Auto-confirm the email
      user_metadata: {
        name: 'Test User'
      }
    });
    
    if (userError) {
      throw userError;
    }
    
    console.log('User created successfully:', userData.user);
    
    // Create a profile for the test user
    const userId = userData.user.id;
    
    const profileData = {
      id: userId,
      name: 'Test User',
      bio: 'This is a test account for trying out the ChatNMatch app.',
      gender: 'other',
      birth_date: '1990-01-01',
      location: 'Test City',
      avatar_url: 'https://randomuser.me/api/portraits/lego/1.jpg',
      interests: ['testing', 'dating apps', 'technology'],
      preferences: {
        gender_preference: 'all',
        age_min: 18,
        age_max: 50,
        distance: 50
      }
    };
    
    console.log('Creating profile for test user...');
    
    const { error: profileError } = await supabase
      .from('profiles')
      .upsert(profileData);
    
    if (profileError) {
      throw profileError;
    }
    
    console.log('Profile created successfully!');
    console.log('\nTest account details:');
    console.log('Email:', email);
    console.log('Password:', password);
    console.log('\nYou can now log in with these credentials at your app.');
    
  } catch (error) {
    console.error('Error creating test account:', error);
  }
}

createTestAccount();
