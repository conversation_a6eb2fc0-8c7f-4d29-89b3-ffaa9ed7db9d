
import { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger, <PERSON><PERSON>Content } from "@/components/ui/tabs";
import ChatInterface from "@/components/ChatInterface";
import Navigation from "@/components/Navigation";
import UserChats from "./UserChats";

const Chat = () => {
  const [activeTab, setActiveTab] = useState("ai");
  
  useEffect(() => {
    // Force scroll to top when component mounts
    window.scrollTo(0, 0);
  }, []);
  
  return (
    <div className="min-h-screen flex flex-col">
      <div className="p-4 border-b sticky top-0 z-10 bg-background/80 backdrop-blur-lg">
        <h1 className="text-2xl font-medium mb-4 font-sans">Chat</h1>
      </div>
      
      <div className="flex-1">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full h-full">
          <div className="px-4 sticky top-16 z-10 bg-background/80 backdrop-blur-lg">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="ai" className="font-sans">AI Assistant</TabsTrigger>
              <TabsTrigger value="chats" className="font-sans">Messages</TabsTrigger>
            </TabsList>
          </div>
          
          <TabsContent value="ai" className="h-full m-0 pb-16">
            <ChatInterface />
          </TabsContent>
          
          <TabsContent value="chats" className="h-full m-0 pb-16">
            <UserChats />
          </TabsContent>
        </Tabs>
      </div>
      
      <Navigation />
    </div>
  );
};

export default Chat;
