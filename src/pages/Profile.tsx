
import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useAuth } from "@/contexts/AuthContext";
import { updateProfile } from "@/lib/supabase";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Save, ChevronLeft, Heart, Trash2, Share2, Bell, Shield, Lock, LogOut } from "lucide-react";
import PhotoUploader from "@/components/PhotoUploader";
import { toast } from "sonner";
import Navigation from "@/components/Navigation";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";

const profileFormSchema = z.object({
  name: z.string().min(2, { message: "Name must be at least 2 characters." }),
  age: z.coerce.number().min(18, { message: "You must be at least 18 years old." }).max(120),
  gender: z.string().min(1, { message: "Please select a gender." }),
  interestedIn: z.string().min(1, { message: "Please select your preference." }),
  location: z.string().min(2, { message: "Location must be at least 2 characters." }),
  bio: z.string().max(500, { message: "Bio must not exceed 500 characters." }),
  occupation: z.string().optional(),
  education: z.string().optional(),
  height: z.coerce.number().min(100, { message: "Please enter a valid height." }).max(250).optional(),
});

type ProfileFormValues = z.infer<typeof profileFormSchema>;

const defaultValues: Partial<ProfileFormValues> = {
  name: "Alex Johnson",
  age: 28,
  gender: "male",
  interestedIn: "female",
  location: "New York, NY",
  bio: "Coffee enthusiast, avid traveler, and tech lover. Looking for someone to share adventures with.",
  occupation: "Software Engineer",
  education: "Stanford University",
  height: 180,
};

const Profile = () => {
  const navigate = useNavigate();
  const { user, profile: userProfile, updateUserProfile } = useAuth();
  const [activeTab, setActiveTab] = useState("profile");

  const form = useForm<ProfileFormValues>({
    resolver: zodResolver(profileFormSchema),
    defaultValues: userProfile ? {
      name: userProfile.name,
      age: userProfile.age,
      gender: userProfile.gender,
      interestedIn: userProfile.interested_in,
      location: userProfile.location,
      bio: userProfile.bio,
      occupation: userProfile.occupation || '',
      education: userProfile.education || '',
      height: userProfile.height || 170,
    } : defaultValues,
  });

  // Fetch profile from API
  useEffect(() => {
    const fetchProfile = async () => {
      if (!user) return;

      try {
        const { data, error } = await api.get('profile');

        if (error) {
          throw error;
        }

        if (data && data.profile) {
          const profile = data.profile;

          // Calculate age from birth_date
          let age = 25; // Default age
          if (profile.birth_date) {
            const today = new Date();
            const birth = new Date(profile.birth_date);
            age = today.getFullYear() - birth.getFullYear();
            const monthDiff = today.getMonth() - birth.getMonth();
            if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
              age--;
            }
          }

          // Map preferences to form fields
          const preferences = profile.preferences || {};

          // Update form with profile data
          form.reset({
            name: profile.name || '',
            age: age,
            gender: profile.gender || '',
            interestedIn: preferences.gender_preference || 'everyone',
            location: profile.location || '',
            bio: profile.bio || '',
            occupation: profile.occupation || '',
            education: profile.education || '',
            height: profile.height || 170,
          });

          // Update user profile in context if available
          if (updateUserProfile) {
            updateUserProfile({
              ...profile,
              age: age,
              interested_in: preferences.gender_preference || 'everyone'
            });
          }
        }
      } catch (error) {
        console.error('Error fetching profile:', error);
        toast.error('Failed to load profile');

        // If userProfile is available from context, use that
        if (userProfile) {
          form.reset({
            name: userProfile.name,
            age: userProfile.age,
            gender: userProfile.gender,
            interestedIn: userProfile.interested_in,
            location: userProfile.location,
            bio: userProfile.bio,
            occupation: userProfile.occupation || '',
            education: userProfile.education || '',
            height: userProfile.height || 170,
          });
        }
      }
    };

    fetchProfile();
  }, [user, form, updateUserProfile, userProfile]);

  const onSubmit = async (data: ProfileFormValues) => {
    if (!user) return;

    try {
      // Convert form data to profile format
      const profileData = {
        name: data.name,
        // Convert age to birth_date (approximate)
        birth_date: new Date(new Date().getFullYear() - data.age, 0, 1).toISOString().split('T')[0],
        gender: data.gender,
        location: data.location,
        bio: data.bio,
        occupation: data.occupation,
        education: data.education,
        height: data.height,
        // Set preferences
        preferences: {
          gender_preference: data.interestedIn,
          age_min: Math.max(18, data.age - 10),
          age_max: data.age + 10,
          distance: 50 // Default distance
        }
      };

      // Update profile using Worker API
      const { data: updatedProfile, error } = await api.post('profile', profileData);

      if (error) {
        throw error;
      }

      // Update local profile state if context method is available
      if (updateUserProfile) {
        await updateUserProfile({
          ...updatedProfile.profile,
          age: data.age,
          interested_in: data.interestedIn
        });
      }

      toast.success("Profile updated successfully!");
    } catch (error) {
      console.error('Error updating profile:', error);
      toast.error("Failed to update profile");
    }
  };

  return (
    <div className="min-h-screen flex flex-col bg-background pb-16">
      <header className="p-4 border-b flex items-center justify-between sticky top-0 z-10 bg-background/80 backdrop-blur-lg">
        <div className="flex items-center">
          <Button
            variant="ghost"
            size="icon"
            className="mr-2"
            onClick={() => navigate(-1)}
          >
            <ChevronLeft className="h-5 w-5" />
          </Button>
          <h1 className="text-xl font-medium">Profile</h1>
        </div>
        <Button
          size="sm"
          className="rounded-full bg-primary hover:bg-primary/90"
          onClick={form.handleSubmit(onSubmit)}
        >
          <Save className="h-4 w-4 mr-1" />
          Save
        </Button>
      </header>

      <div className="flex-1 p-4">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-2 mb-6">
            <TabsTrigger value="profile">Edit Profile</TabsTrigger>
            <TabsTrigger value="settings">Settings</TabsTrigger>
          </TabsList>

          <TabsContent value="profile">
            <div className="mb-6">
              {user && (
                <PhotoUploader
                  userId={user.id}
                  avatarUrl={userProfile?.avatar_url || null}
                  onAvatarChange={(url) => {
                    // Update the avatar URL in the form data
                    if (updateUserProfile && userProfile) {
                      updateUserProfile({
                        ...userProfile,
                        avatar_url: url
                      });
                    }
                  }}
                />
              )}
            </div>

            <Form {...form}>
              <form className="space-y-6">
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Name</FormLabel>
                      <FormControl>
                        <Input placeholder="Your name" {...field} />
                      </FormControl>
                      <FormDescription>
                        This is how you'll appear in the app.
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="age"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Age</FormLabel>
                        <FormControl>
                          <Input type="number" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="height"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Height (cm)</FormLabel>
                        <FormControl>
                          <Input type="number" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="gender"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Gender</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select gender" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="male">Male</SelectItem>
                            <SelectItem value="female">Female</SelectItem>
                            <SelectItem value="non-binary">Non-binary</SelectItem>
                            <SelectItem value="other">Other</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="interestedIn"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Interested In</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select preference" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="male">Men</SelectItem>
                            <SelectItem value="female">Women</SelectItem>
                            <SelectItem value="everyone">Everyone</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <FormField
                  control={form.control}
                  name="location"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Location</FormLabel>
                      <FormControl>
                        <Input placeholder="City, Country" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="occupation"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Occupation</FormLabel>
                        <FormControl>
                          <Input placeholder="Your job" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="education"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Education</FormLabel>
                        <FormControl>
                          <Input placeholder="University/School" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <FormField
                  control={form.control}
                  name="bio"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Bio</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Tell us about yourself..."
                          className="resize-none"
                          {...field}
                        />
                      </FormControl>
                      <FormDescription>
                        Maximum 500 characters.
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div>
                  <h3 className="text-lg font-medium mb-4">Interests</h3>
                  <div className="flex flex-wrap gap-2">
                    {["Travel", "Music", "Art", "Sports", "Food", "Reading", "Photography", "Movies", "Technology", "Fitness"].map((interest, index) => {
                      // Use a deterministic approach based on the index
                      const isSelected = [0, 2, 5, 8].includes(index);
                      return (
                        <Button
                          key={interest}
                          variant={isSelected ? "default" : "outline"}
                          size="sm"
                          className={isSelected ? "bg-primary hover:bg-primary/90" : ""}
                        >
                          {interest}
                        </Button>
                      );
                    })}
                    <Button
                      variant="outline"
                      size="sm"
                    >
                      + Add
                    </Button>
                  </div>
                </div>

                <div>
                  <h3 className="text-lg font-medium mb-4">Photos</h3>
                  <div className="grid grid-cols-3 gap-2">
                    {Array.from({ length: 6 }).map((_, i) => (
                      <div key={i} className="aspect-square rounded-md bg-muted flex items-center justify-center relative overflow-hidden">
                        {i < 3 ? (
                          <>
                            <img
                              src={i === 0 ? "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d" :
                                   i === 1 ? "https://images.unsplash.com/photo-1500648767791-00dcc994a43e" :
                                   "https://images.unsplash.com/photo-1494790108377-be9c29b29330"}
                              alt={`Photo ${i+1}`}
                              className="w-full h-full object-cover"
                            />
                            <Button
                              variant="destructive"
                              size="icon"
                              className="absolute top-1 right-1 h-6 w-6 rounded-full"
                            >
                              <Trash2 className="h-3 w-3" />
                            </Button>
                          </>
                        ) : (
                          <Camera className="h-6 w-6 text-muted-foreground" />
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              </form>
            </Form>
          </TabsContent>

          <TabsContent value="settings">
            <div className="space-y-6">
              <div className="space-y-3">
                <h3 className="text-lg font-medium">Account</h3>
                <div className="rounded-lg border p-4 space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                      <Share2 className="h-5 w-5 text-muted-foreground" />
                      <span>Share Profile</span>
                    </div>
                    <ChevronLeft className="h-5 w-5 rotate-180" />
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                      <Bell className="h-5 w-5 text-muted-foreground" />
                      <span>Notifications</span>
                    </div>
                    <ChevronLeft className="h-5 w-5 rotate-180" />
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                      <Shield className="h-5 w-5 text-muted-foreground" />
                      <span>Privacy</span>
                    </div>
                    <ChevronLeft className="h-5 w-5 rotate-180" />
                  </div>
                </div>
              </div>

              <div className="space-y-3">
                <h3 className="text-lg font-medium">Discovery Settings</h3>
                <div className="rounded-lg border p-4 space-y-4">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="location">Location</Label>
                    <div className="flex items-center gap-2">
                      <span className="text-sm text-muted-foreground">New York</span>
                      <ChevronLeft className="h-5 w-5 rotate-180" />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <Label htmlFor="distance">Maximum Distance</Label>
                      <span className="text-sm text-muted-foreground">25 miles</span>
                    </div>
                    <input
                      type="range"
                      id="distance"
                      min="1"
                      max="100"
                      defaultValue="25"
                      className="w-full accent-love-500"
                    />
                  </div>

                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <Label htmlFor="age-range">Age Range</Label>
                      <span className="text-sm text-muted-foreground">22 - 35</span>
                    </div>
                    <input
                      type="range"
                      id="age-range"
                      min="18"
                      max="65"
                      defaultValue="30"
                      className="w-full accent-love-500"
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <Label htmlFor="global">Global Mode</Label>
                      <p className="text-sm text-muted-foreground">Match with people worldwide</p>
                    </div>
                    <Switch id="global" />
                  </div>
                </div>
              </div>

              <div className="space-y-3">
                <h3 className="text-lg font-medium">App Settings</h3>
                <div className="rounded-lg border p-4 space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                      <Lock className="h-5 w-5 text-muted-foreground" />
                      <span>Change Password</span>
                    </div>
                    <ChevronLeft className="h-5 w-5 rotate-180" />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <Label htmlFor="theme">Dark Mode</Label>
                    </div>
                    <Switch id="theme" />
                  </div>
                </div>
              </div>

              <Button
                variant="destructive"
                className="w-full mt-6"
              >
                <LogOut className="h-4 w-4 mr-2" />
                Sign Out
              </Button>
            </div>
          </TabsContent>
        </Tabs>
      </div>

      <Navigation />
    </div>
  );
};

export default Profile;
