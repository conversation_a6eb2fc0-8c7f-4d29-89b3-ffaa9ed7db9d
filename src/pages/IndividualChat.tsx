import { useState, useRef, useEffect } from 'react';
import { useNavigate, useLocation, useParams } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { getMessages, sendMessage, getProfile, api } from '@/lib/supabase';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Send, ChevronLeft, Phone, Video, MoreVertical } from 'lucide-react';
import { toast } from 'sonner';
import ConversationBubble from '@/components/ConversationBubble';
import Navigation from '@/components/Navigation';

type Message = {
  id: string;
  content: string;
  sender: 'user' | 'other';
  timestamp: Date;
};

const IndividualChat = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { id } = useParams<{ id: string }>();
  const { user } = useAuth();
  const [partnerProfile, setPartnerProfile] = useState<any>(location.state?.profile || {
    id: id || '1',
    name: 'Loading...',
    imageUrl: 'https://images.unsplash.com/photo-1494790108377-be9c29b29330?ixlib=rb-1.2.1&auto=format&fit=crop&w=256&q=80',
  });

  const [inputValue, setInputValue] = useState('');
  const [messages, setMessages] = useState<Message[]>([]);
  const [isTyping, setIsTyping] = useState(false);
  const [loading, setLoading] = useState(true);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Fetch partner profile if not provided in location state
  useEffect(() => {
    const fetchPartnerProfile = async () => {
      if (!id || location.state?.profile) return;

      try {
        // Use Worker API instead of direct Supabase call
        const { data, error } = await api.get(`profiles/${id}`);

        if (error) {
          throw error;
        }

        if (data) {
          setPartnerProfile({
            id: data.id,
            name: data.name,
            age: data.age,
            location: data.location,
            imageUrl: data.avatar_url || 'https://images.unsplash.com/photo-1494790108377-be9c29b29330?ixlib=rb-1.2.1&auto=format&fit=crop&w=256&q=80',
          });
        }
      } catch (error) {
        console.error('Error fetching partner profile:', error);
        toast.error('Failed to load profile');
      }
    };

    fetchPartnerProfile();
  }, [id, location.state]);

  // Fetch messages
  useEffect(() => {
    const fetchMessages = async () => {
      if (!user || !id) return;

      try {
        setLoading(true);
        // Get the room ID from location state or use the ID parameter
        const roomId = location.state?.roomId || id;

        // Use Worker API to get messages for this chat room
        const { data, error } = await api.get(`chats/${roomId}/messages`);

        if (error) {
          throw error;
        }

        if (data && data.messages) {
          // Convert messages to our app's format
          const formattedMessages = data.messages.map(msg => ({
            id: msg.id,
            content: msg.content,
            sender: msg.sender_id === user.id ? 'user' : 'other',
            timestamp: new Date(msg.created_at || Date.now()),
          }));

          setMessages(formattedMessages);
        }
      } catch (error) {
        console.error('Error fetching messages:', error);
        toast.error('Failed to load messages');

        // Set a default welcome message if no messages are loaded
        if (messages.length === 0) {
          setMessages([
            {
              id: '1',
              content: `Hi there! I saw that we matched. How are you doing today?`,
              sender: 'other',
              timestamp: new Date(Date.now() - 1000 * 60 * 5), // 5 minutes ago
            },
          ]);
        }
      } finally {
        setLoading(false);
      }
    };

    fetchMessages();

    // Set up real-time subscription for new messages
    if (user && id) {
      const roomId = location.state?.roomId || id;

      const subscription = supabase
        .channel(`room:${roomId}`)
        .on('postgres_changes', {
          event: 'INSERT',
          schema: 'public',
          table: 'messages',
          filter: `chat_room_id=eq.${roomId}`
        }, (payload) => {
          // When a new message is received, add it to the messages list
          const newMessage = payload.new;
          if (newMessage && newMessage.sender_id !== user.id) {
            const formattedMessage = {
              id: newMessage.id,
              content: newMessage.content,
              sender: 'other',
              timestamp: new Date(newMessage.created_at || Date.now()),
            };
            setMessages(prev => [...prev, formattedMessage]);
          }
        })
        .subscribe();

      return () => {
        supabase.removeChannel(subscription);
      };
    }
  }, [user, id, location.state]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const handleSendMessage = async () => {
    if (!inputValue.trim() || !user || !id) return;

    // Get the room ID from location state or use the ID parameter
    const roomId = location.state?.roomId || id;

    // Create message object
    const messageContent = inputValue.trim();
    const userMessage: Message = {
      id: Date.now().toString(),
      content: messageContent,
      sender: 'user',
      timestamp: new Date(),
    };

    // Update UI immediately
    setMessages(prev => [...prev, userMessage]);
    setInputValue('');

    try {
      // Send message using Worker API
      const { error } = await api.post(`chats/${roomId}/messages`, {
        content: messageContent
      });

      if (error) {
        throw error;
      }

      // We don't need to simulate a response anymore since we're using real-time subscriptions
      // The response will come through the subscription if the other user replies

      // But for demo purposes, we can still simulate typing and responses
      // This would be removed in a production app with real users
      if (process.env.NODE_ENV !== 'production') {
        setIsTyping(true);

        setTimeout(() => {
          const responses = [
            "That sounds great! I'd love to hear more about it.",
            "Interesting! Tell me more about yourself.",
            "I've been thinking about that too! What do you enjoy doing in your free time?",
            "I'd love to meet up sometime if you're interested?",
            "That's so cool! I have similar interests.",
          ];

          const responseMessage: Message = {
            id: (Date.now() + 1).toString(),
            content: responses[Math.floor(Math.random() * responses.length)],
            sender: 'other',
            timestamp: new Date(),
          };

          setMessages(prev => [...prev, responseMessage]);
          setIsTyping(false);
        }, 2000);
      }
    } catch (error) {
      console.error('Error sending message:', error);
      toast.error('Failed to send message');
      // Remove the message from UI if it failed to send
      setMessages(prev => prev.filter(msg => msg.id !== userMessage.id));
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSendMessage();
    }
  };

  return (
    <div className="flex flex-col h-screen bg-background">
      <header className="p-4 border-b flex items-center justify-between sticky top-0 z-10 bg-background/80 backdrop-blur-lg">
        <div className="flex items-center">
          <Button
            variant="ghost"
            size="icon"
            className="mr-2"
            onClick={() => navigate('/chats')}
          >
            <ChevronLeft className="h-5 w-5" />
          </Button>

          <div className="flex items-center">
            <div className="relative">
              <img
                src={partnerProfile.imageUrl}
                alt={partnerProfile.name}
                className="w-10 h-10 rounded-full object-cover"
              />
              <div className="absolute bottom-0 right-0 w-3 h-3 bg-green-500 rounded-full border-2 border-background"></div>
            </div>

            <div className="ml-3">
              <h2 className="text-base font-medium">{partnerProfile.name}</h2>
              <p className="text-xs text-muted-foreground">Online now</p>
            </div>
          </div>
        </div>

        <div className="flex items-center">
          <Button variant="ghost" size="icon" className="text-muted-foreground">
            <Phone className="h-5 w-5" />
          </Button>
          <Button variant="ghost" size="icon" className="text-muted-foreground">
            <Video className="h-5 w-5" />
          </Button>
          <Button variant="ghost" size="icon" className="text-muted-foreground">
            <MoreVertical className="h-5 w-5" />
          </Button>
        </div>
      </header>

      <div className="flex-1 overflow-y-auto p-4 pb-24">
        <div className="space-y-4">
          <div className="flex justify-center">
            <div className="px-3 py-1 rounded-full bg-muted text-xs text-muted-foreground">
              Today
            </div>
          </div>

          {messages.map((message) => (
            <div key={message.id} className="mb-4">
              <ConversationBubble
                message={{
                  ...message,
                  sender: message.sender === 'other' ? 'ai' : 'user',
                }}
              />
            </div>
          ))}

          {isTyping && (
            <div className="flex items-center gap-2 ml-12 mb-4">
              <div className="h-2 w-2 bg-muted-foreground/50 rounded-full animate-pulse"></div>
              <div className="h-2 w-2 bg-muted-foreground/50 rounded-full animate-pulse animate-delay-200"></div>
              <div className="h-2 w-2 bg-muted-foreground/50 rounded-full animate-pulse animate-delay-400"></div>
            </div>
          )}

          <div ref={messagesEndRef} />
        </div>
      </div>

      <div className="p-4 border-t bg-background/80 backdrop-blur-md fixed bottom-16 left-0 right-0 safe-bottom">
        <div className="flex items-center gap-2 max-w-3xl mx-auto">
          <Input
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            onKeyDown={handleKeyDown}
            placeholder="Type a message..."
            className="flex-1 h-12 px-4 bg-background border-muted rounded-full"
          />
          <Button
            onClick={handleSendMessage}
            size="icon"
            className="h-12 w-12 rounded-full bg-primary hover:bg-primary/80 text-white"
            disabled={!inputValue.trim()}
          >
            <Send className="h-5 w-5" />
          </Button>
        </div>
      </div>

      <Navigation />
    </div>
  );
};

export default IndividualChat;
