
import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useAuth } from "@/contexts/AuthContext";
import { getConversations, api } from "@/lib/supabase";
import { Input } from "@/components/ui/input";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
import Navigation from "@/components/Navigation";
import { Search, MoreVertical } from "lucide-react";
import { toast } from "sonner";

type ChatPreview = {
  id: string;
  name: string;
  lastMessage: string;
  time: string;
  avatar: string;
  unread: boolean;
  partnerId: string;
};

const mockChats: ChatPreview[] = [
  {
    id: '1',
    name: '<PERSON>',
    lastMessage: 'I would love to check out that restaurant!',
    time: '2m ago',
    avatar: 'https://images.unsplash.com/photo-1494790108377-be9c29b29330?ixlib=rb-1.2.1&auto=format&fit=crop&w=256&q=80',
    unread: true,
    partnerId: '00000000-0000-0000-0000-000000000002'
  },
  {
    id: '2',
    name: '<PERSON>',
    lastMessage: 'When are you free this weekend?',
    time: '1h ago',
    avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-1.2.1&auto=format&fit=crop&w=256&q=80',
    unread: false,
    partnerId: '00000000-0000-0000-0000-000000000003'
  },
  {
    id: '3',
    name: 'Sophia',
    lastMessage: 'That sounds like a plan! 😊',
    time: '3h ago',
    avatar: 'https://images.unsplash.com/photo-1580489944761-15a19d654956?ixlib=rb-1.2.1&auto=format&fit=crop&w=256&q=80',
    unread: false,
    partnerId: '00000000-0000-0000-0000-000000000004'
  },
  {
    id: '4',
    name: 'Ethan',
    lastMessage: 'Hey! Are you interested in going hiking this weekend?',
    time: 'Yesterday',
    avatar: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?ixlib=rb-1.2.1&auto=format&fit=crop&w=256&q=80',
    unread: true,
    partnerId: '00000000-0000-0000-0000-000000000005'
  },
  {
    id: '5',
    name: 'Olivia',
    lastMessage: 'Thanks for the recommendation!',
    time: 'Yesterday',
    avatar: 'https://images.unsplash.com/photo-1531746020798-e6953c6e8e04?ixlib=rb-1.2.1&auto=format&fit=crop&w=256&q=80',
    unread: false,
    partnerId: '00000000-0000-0000-0000-000000000001'
  },
];

const UserChats = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const [filter, setFilter] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [chats, setChats] = useState<ChatPreview[]>(mockChats);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchConversations = async () => {
      if (!user) return;

      try {
        setLoading(true);

        // Use Worker API to get chat rooms
        const { data, error } = await api.get(`chats`);

        if (error) {
          throw error;
        }

        if (data && data.chatRooms) {
          // Convert chat rooms to our app's format
          const formattedChats = data.chatRooms.map(room => {
            // Find the other participant (not the current user)
            const otherParticipant = room.profiles.find(
              (profile: any) => profile.id !== user.id
            ) || {};

            // Get the last message if available
            const lastMessage = room.last_message || 'Start a conversation';
            const lastMessageTime = room.last_message_time || room.created_at;

            return {
              id: room.id,
              name: otherParticipant.name || 'Unknown User',
              lastMessage: lastMessage,
              time: formatTime(new Date(lastMessageTime)),
              avatar: otherParticipant.avatar_url || 'https://images.unsplash.com/photo-1494790108377-be9c29b29330?ixlib=rb-1.2.1&auto=format&fit=crop&w=256&q=80',
              unread: room.unread_count > 0,
              partnerId: otherParticipant.id
            };
          });

          setChats(formattedChats);
        }
      } catch (error) {
        console.error('Error fetching conversations:', error);
        toast.error('Failed to load conversations');

        // Fallback to mock data if API fails
        setChats(mockChats);
      } finally {
        setLoading(false);
      }
    };

    fetchConversations();
  }, [user]);

  // Helper function to format timestamp
  const formatTime = (date: Date) => {
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / 60000);
    const diffHours = Math.floor(diffMs / 3600000);
    const diffDays = Math.floor(diffMs / 86400000);

    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins}m ago`;
    if (diffHours < 24) return `${diffHours}h ago`;
    if (diffDays < 2) return 'Yesterday';
    return date.toLocaleDateString();
  };

  const filteredChats = chats.filter(chat => {
    const matchesSearch = chat.name.toLowerCase().includes(searchTerm.toLowerCase());
    if (filter === 'all') return matchesSearch;
    if (filter === 'unread') return matchesSearch && chat.unread;
    return matchesSearch;
  });

  const handleChatClick = (chat: ChatPreview) => {
    toast.success(`Opening chat with ${chat.name}`);
    const profile = {
      id: chat.partnerId,
      name: chat.name,
      imageUrl: chat.avatar
    };
    navigate(`/chat/${chat.id}`, { state: { profile, roomId: chat.id } });
  };

  return (
    <div className="min-h-screen flex flex-col bg-background">
      <header className="p-4 border-b sticky top-0 z-10 bg-background/80 backdrop-blur-lg">
        <h1 className="text-2xl font-medium mb-4">Messages</h1>

        <div className="relative mb-4">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search messages..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-9 h-10 rounded-full"
          />
        </div>

        <Tabs value={filter} onValueChange={setFilter} className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="all">All</TabsTrigger>
            <TabsTrigger value="unread">Unread</TabsTrigger>
          </TabsList>
        </Tabs>
      </header>

      <div className="flex-1 p-2 pb-20">
        {filteredChats.length > 0 ? (
          <div className="space-y-1">
            {filteredChats.map((chat) => (
              <div
                key={chat.id}
                className="flex items-center p-3 rounded-lg hover:bg-muted/50 transition-colors cursor-pointer"
                onClick={() => handleChatClick(chat)}
              >
                <div className="relative">
                  <img
                    src={chat.avatar}
                    alt={chat.name}
                    className="w-14 h-14 rounded-full object-cover mr-3"
                  />
                  {chat.unread && (
                    <div className="absolute -top-1 -right-1 w-4 h-4 bg-primary rounded-full border-2 border-background" />
                  )}
                </div>

                <div className="flex-1 min-w-0">
                  <div className="flex justify-between items-baseline">
                    <h3 className={`text-base font-medium truncate ${chat.unread ? 'text-foreground' : 'text-muted-foreground'}`}>
                      {chat.name}
                    </h3>
                    <span className="text-xs text-muted-foreground ml-2 whitespace-nowrap">
                      {chat.time}
                    </span>
                  </div>
                  <p className={`text-sm truncate ${chat.unread ? 'text-foreground font-medium' : 'text-muted-foreground'}`}>
                    {chat.lastMessage}
                  </p>
                </div>

                <button className="ml-2 text-muted-foreground">
                  <MoreVertical className="h-5 w-5" />
                </button>
              </div>
            ))}
          </div>
        ) : (
          <div className="flex flex-col items-center justify-center h-64 text-center">
            <p className="text-muted-foreground mb-2">No messages found</p>
            <p className="text-sm">Try adjusting your search or find new matches</p>
          </div>
        )}
      </div>

      <Navigation />
    </div>
  );
};

export default UserChats;
