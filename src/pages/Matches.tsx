
import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useAuth } from "@/contexts/AuthContext";
import { getPotentialMatches, likeProfile, api } from "@/lib/supabase";
import Navigation from "@/components/Navigation";
import ProfileCard from "@/components/ProfileCard";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Search, Filter, Heart } from "lucide-react";
import { toast } from "sonner";

const mockProfiles = [
  {
    id: '1',
    name: '<PERSON>',
    age: 28,
    location: 'New York',
    bio: 'Art lover, coffee enthusiast, and avid traveler. Looking for someone to explore the city with.',
    interests: ['Art', 'Travel', 'Coffee'],
    imageUrl: 'https://images.unsplash.com/photo-1494790108377-be9c29b29330?ixlib=rb-1.2.1&auto=format&fit=crop&w=256&q=80',
    isLiked: false,
  },
  {
    id: '2',
    name: '<PERSON>',
    age: 32,
    location: 'Boston',
    bio: 'Software engineer by day, chef by night. I enjoy hiking, reading, and trying new restaurants.',
    interests: ['Cooking', 'Hiking', 'Technology'],
    imageUrl: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-1.2.1&auto=format&fit=crop&w=256&q=80',
    isLiked: false,
  },
  {
    id: '3',
    name: 'Sophia',
    age: 30,
    location: 'Chicago',
    bio: 'Musician and yoga instructor. Looking for someone who values mindfulness and creativity.',
    interests: ['Music', 'Yoga', 'Mindfulness'],
    imageUrl: 'https://images.unsplash.com/photo-1580489944761-15a19d654956?ixlib=rb-1.2.1&auto=format&fit=crop&w=256&q=80',
    isLiked: false,
  },
  {
    id: '4',
    name: 'Ethan',
    age: 27,
    location: 'San Francisco',
    bio: 'Startup founder and outdoor enthusiast. Passionate about sustainable technology and rock climbing.',
    interests: ['Startups', 'Rock Climbing', 'Sustainability'],
    imageUrl: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?ixlib=rb-1.2.1&auto=format&fit=crop&w=256&q=80',
    isLiked: false,
  },
  {
    id: '5',
    name: 'Olivia',
    age: 29,
    location: 'Austin',
    bio: 'Veterinarian and animal lover. Enjoy reading, gardening and live music.',
    interests: ['Animals', 'Reading', 'Gardening'],
    imageUrl: 'https://images.unsplash.com/photo-1531746020798-e6953c6e8e04?ixlib=rb-1.2.1&auto=format&fit=crop&w=256&q=80',
    isLiked: false,
  },
  {
    id: '6',
    name: 'James',
    age: 33,
    location: 'Seattle',
    bio: 'Photographer and film enthusiast. Love to travel and experience different cultures.',
    interests: ['Photography', 'Film', 'Travel'],
    imageUrl: 'https://images.unsplash.com/photo-1506794778202-cad84cf45f1d?ixlib=rb-1.2.1&auto=format&fit=crop&w=256&q=80',
    isLiked: false,
  },
];

const Matches = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const [searchTerm, setSearchTerm] = useState("");
  const [profiles, setProfiles] = useState(mockProfiles);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchMatches = async () => {
      if (!user) return;

      try {
        setLoading(true);

        // Use the Worker API to get potential matches
        const { data, error } = await api.get(`matches`);

        if (error) {
          throw error;
        }

        if (data && data.matches) {
          // Convert profiles to our app's format
          const formattedProfiles = data.matches.map(profile => ({
            id: profile.id,
            name: profile.name,
            age: calculateAge(profile.birth_date),
            location: profile.location,
            bio: profile.bio,
            interests: profile.interests || [],
            imageUrl: profile.avatar_url || 'https://images.unsplash.com/photo-1494790108377-be9c29b29330?ixlib=rb-1.2.1&auto=format&fit=crop&w=256&q=80',
            isLiked: false,
          }));

          setProfiles(formattedProfiles);
        }
      } catch (error) {
        console.error('Error fetching matches:', error);
        toast.error('Failed to load matches');

        // Fallback to mock data if API fails
        setProfiles(mockProfiles);
      } finally {
        setLoading(false);
      }
    };

    // Helper function to calculate age from birth date
    const calculateAge = (birthDate: string) => {
      if (!birthDate) return 25; // Default age
      const today = new Date();
      const birth = new Date(birthDate);
      let age = today.getFullYear() - birth.getFullYear();
      const monthDiff = today.getMonth() - birth.getMonth();
      if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
        age--;
      }
      return age;
    };

    fetchMatches();
  }, [user]);

  const filteredProfiles = profiles.filter(profile =>
    profile.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    profile.location.toLowerCase().includes(searchTerm.toLowerCase()) ||
    profile.interests.some(interest =>
      interest.toLowerCase().includes(searchTerm.toLowerCase())
    )
  );

  const handleLike = async (profileId: string) => {
    if (!user) return;

    // Find the profile
    const profileToLike = profiles.find(p => p.id === profileId);
    if (!profileToLike) return;

    // Update UI immediately
    setProfiles(prev =>
      prev.map(profile => {
        if (profile.id === profileId) {
          return { ...profile, isLiked: !profile.isLiked };
        }
        return profile;
      })
    );

    try {
      // Send like to Worker API
      const { data, error } = await api.post(`like`, {
        profileId: profileId
      });

      if (error) {
        throw error;
      }

      // Show appropriate toast
      if (data?.isMatch) {
        toast.success(`${profileToLike.name} liked you back! It's a match!`);

        // If there's a chat room created, we could navigate to it
        if (data.chatRoom) {
          // Optionally navigate to the chat
          // navigate(`/chat/${data.chatRoom.id}`, { state: { profile: profileToLike } });
        }
      } else {
        toast.success(`You liked ${profileToLike.name}!`);
      }

      // Remove the profile from the list if it's been liked
      setProfiles(prev => prev.filter(profile => profile.id !== profileId));

    } catch (error) {
      console.error('Error liking profile:', error);
      toast.error('Failed to like profile');

      // Revert UI change on error
      setProfiles(prev =>
        prev.map(profile => {
          if (profile.id === profileId) {
            return { ...profile, isLiked: !profile.isLiked };
          }
          return profile;
        })
      );
    }
  };

  const handleChat = (profile: any) => {
    toast.success(`Starting chat with ${profile.name}`);
    navigate(`/chat/${profile.id}`, { state: { profile } });
  };

  return (
    <div className="min-h-screen flex flex-col">
      <header className="p-4 border-b sticky top-0 z-10 bg-background/80 backdrop-blur-lg">
        <div className="flex items-center justify-between mb-4">
          <h1 className="text-2xl font-medium">Discover</h1>
          <Button variant="ghost" size="icon" className="rounded-full text-primary">
            <Heart className="h-5 w-5" />
          </Button>
        </div>

        <div className="flex gap-2">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search by name, location, or interests..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-9 h-10 rounded-full"
            />
          </div>
          <Button variant="outline" size="icon" className="h-10 w-10 rounded-full">
            <Filter className="h-4 w-4" />
          </Button>
        </div>
      </header>

      <div className="flex-1 p-4 pb-20">
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
          {filteredProfiles.map((profile) => (
            <div key={profile.id} className="animate-fade-up">
              <ProfileCard
                profile={profile}
                onLike={() => handleLike(profile.id)}
                onChat={handleChat}
              />
            </div>
          ))}
        </div>

        {filteredProfiles.length === 0 && (
          <div className="flex flex-col items-center justify-center h-64 text-center">
            <p className="text-muted-foreground mb-2">No matches found</p>
            <p className="text-sm">Try adjusting your search or ask the AI assistant for help</p>
          </div>
        )}
      </div>

      <Navigation />
    </div>
  );
};

export default Matches;
