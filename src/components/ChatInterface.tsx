
import { useState, useRef, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Send } from 'lucide-react';
import { toast } from 'sonner';
import ConversationBubble from './ConversationBubble';
import ProfileCard from './ProfileCard';

type Message = {
  id: string;
  content: string;
  sender: 'user' | 'ai';
  timestamp: Date;
  showProfiles?: boolean;
};

const initialMessages: Message[] = [
  {
    id: '1',
    content: "Hi there! I'm your AI dating assistant. I'll help you find your perfect match. What kind of person are you looking for?",
    sender: 'ai',
    timestamp: new Date(),
  },
];

const mockProfiles = [
  {
    id: '1',
    name: '<PERSON>',
    age: 28,
    location: 'New York',
    bio: 'Art lover, coffee enthusiast, and avid traveler. Looking for someone to explore the city with.',
    interests: ['Art', 'Travel', 'Coffee'],
    imageUrl: 'https://images.unsplash.com/photo-1494790108377-be9c29b29330?ixlib=rb-1.2.1&auto=format&fit=crop&w=256&q=80',
    isLiked: false,
  },
  {
    id: '2',
    name: '<PERSON>',
    age: 32,
    location: 'Boston',
    bio: 'Software engineer by day, chef by night. I enjoy hiking, reading, and trying new restaurants.',
    interests: ['Cooking', 'Hiking', 'Technology'],
    imageUrl: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-1.2.1&auto=format&fit=crop&w=256&q=80',
    isLiked: false,
  },
  {
    id: '3',
    name: 'Sophia',
    age: 30,
    location: 'Chicago',
    bio: 'Musician and yoga instructor. Looking for someone who values mindfulness and creativity.',
    interests: ['Music', 'Yoga', 'Mindfulness'],
    imageUrl: 'https://images.unsplash.com/photo-1580489944761-15a19d654956?ixlib=rb-1.2.1&auto=format&fit=crop&w=256&q=80',
    isLiked: false,
  }
];

const ChatInterface = () => {
  const navigate = useNavigate();
  const [messages, setMessages] = useState<Message[]>(initialMessages);
  const [inputValue, setInputValue] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const [profiles, setProfiles] = useState(mockProfiles);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const handleProfileLike = (profileId: string) => {
    setProfiles(prev =>
      prev.map(profile =>
        profile.id === profileId
          ? { ...profile, isLiked: !profile.isLiked }
          : profile
      )
    );
  };

  const handleChat = (profile: any) => {
    toast.success(`Starting chat with ${profile.name}`);
    navigate(`/chat/${profile.id}`, { state: { profile } });
  };

  const handleSendMessage = () => {
    if (!inputValue.trim()) return;

    // Add user message
    const userMessage: Message = {
      id: Date.now().toString(),
      content: inputValue,
      sender: 'user',
      timestamp: new Date(),
    };

    setMessages(prev => [...prev, userMessage]);
    setInputValue('');
    setIsTyping(true);

    // Always show profiles for certain keywords
    const shouldShowProfiles =
      inputValue.toLowerCase().includes('looking for') ||
      inputValue.toLowerCase().includes('interested in') ||
      inputValue.toLowerCase().includes('match') ||
      inputValue.toLowerCase().includes('date') ||
      inputValue.toLowerCase().includes('show') ||
      inputValue.toLowerCase().includes('find') ||
      inputValue.toLowerCase().includes('suggest') ||
      Math.random() > 0.3; // Increased probability of showing profiles

    // Simulate AI response
    setTimeout(() => {
      const aiMessage: Message = {
        id: (Date.now() + 1).toString(),
        content: generateResponse(inputValue),
        sender: 'ai',
        timestamp: new Date(),
        showProfiles: shouldShowProfiles,
      };

      setMessages(prev => [...prev, aiMessage]);
      setIsTyping(false);
    }, 1500);
  };

  const generateResponse = (input: string): string => {
    const responses = [
      "I understand you're looking for someone who is into that. I've found a few matches that might interest you!",
      "Based on what you've told me, I think these people might be a good match for you.",
      "Thanks for sharing your preferences. I've found some profiles that align with what you're looking for.",
      "I've analyzed your preferences and found these potential matches. Would you like to learn more about any of them?",
      "Here are some people who share similar interests. Take a look and let me know if you'd like to connect with any of them.",
    ];

    return responses[Math.floor(Math.random() * responses.length)];
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSendMessage();
    }
  };

  return (
    <div className="flex flex-col w-full h-screen max-h-screen bg-background">
      <div className="flex-1 overflow-y-auto p-4 pb-24">
        {messages.map((message) => (
          <div key={message.id} className="mb-4">
            <ConversationBubble message={message} />

            {/* Show profile cards if applicable */}
            {message.showProfiles && message.sender === 'ai' && (
              <div className="ml-12 mt-2 overflow-x-auto pb-2 pt-1 flex gap-4 snap-x snap-mandatory">
                {profiles.map((profile) => (
                  <div key={profile.id} className="snap-start shrink-0">
                    <ProfileCard profile={profile} onLike={() => handleProfileLike(profile.id)} onChat={handleChat} />
                  </div>
                ))}
              </div>
            )}
          </div>
        ))}

        {isTyping && (
          <div className="flex items-center gap-2 ml-12 mb-4">
            <div className="h-2 w-2 bg-muted-foreground/50 rounded-full animate-pulse"></div>
            <div className="h-2 w-2 bg-muted-foreground/50 rounded-full animate-pulse animate-delay-200"></div>
            <div className="h-2 w-2 bg-muted-foreground/50 rounded-full animate-pulse animate-delay-400"></div>
          </div>
        )}

        <div ref={messagesEndRef} />
      </div>

      <div className="p-4 border-t bg-background/80 backdrop-blur-md fixed bottom-16 left-0 right-0 safe-bottom">
        <div className="flex items-center gap-2 max-w-3xl mx-auto">
          <Input
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            onKeyDown={handleKeyDown}
            placeholder="Type a message..."
            className="flex-1 h-12 px-4 bg-background border-muted rounded-full"
          />
          <Button
            onClick={handleSendMessage}
            size="icon"
            className="h-12 w-12 rounded-full bg-primary hover:bg-primary/80 text-white"
          >
            <Send className="h-5 w-5" />
          </Button>
        </div>
      </div>
    </div>
  );
};

export default ChatInterface;
