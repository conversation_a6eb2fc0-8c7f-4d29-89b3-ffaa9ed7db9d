import React, { useEffect, useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { supabase } from '@/lib/supabase';
import { toast } from 'sonner';
import { Bell, Heart, MessageCircle } from 'lucide-react';
import { Badge } from '@/components/ui/badge';

const NotificationSystem: React.FC = () => {
  const { user } = useAuth();
  const [unreadCount, setUnreadCount] = useState(0);

  useEffect(() => {
    if (!user) return;

    // Set up subscription for new matches
    const matchSubscription = supabase
      .channel('matches')
      .on('postgres_changes', {
        event: 'INSERT',
        schema: 'public',
        table: 'likes',
        filter: `liked_user_id=eq.${user.id}`
      }, (payload) => {
        // When someone likes the current user
        handleNewLike(payload.new);
      })
      .subscribe();

    // Set up subscription for new messages
    const messageSubscription = supabase
      .channel('messages')
      .on('postgres_changes', {
        event: 'INSERT',
        schema: 'public',
        table: 'messages',
        filter: `receiver_id=eq.${user.id}`
      }, (payload) => {
        // When the current user receives a new message
        handleNewMessage(payload.new);
      })
      .subscribe();

    // Fetch initial unread count
    fetchUnreadCount();

    return () => {
      supabase.removeChannel(matchSubscription);
      supabase.removeChannel(messageSubscription);
    };
  }, [user]);

  const fetchUnreadCount = async () => {
    if (!user) return;

    try {
      // Get unread messages count
      const { data: messages, error: messagesError } = await supabase
        .from('messages')
        .select('id', { count: 'exact' })
        .eq('receiver_id', user.id)
        .eq('is_read', false);

      if (messagesError) throw messagesError;

      // Get new matches count (likes received but not yet viewed)
      const { data: likes, error: likesError } = await supabase
        .from('likes')
        .select('id', { count: 'exact' })
        .eq('liked_user_id', user.id)
        .eq('is_viewed', false);

      if (likesError) throw likesError;

      const totalUnread = (messages?.length || 0) + (likes?.length || 0);
      setUnreadCount(totalUnread);
    } catch (error) {
      console.error('Error fetching unread count:', error);
    }
  };

  const handleNewLike = async (like: any) => {
    try {
      // Get the user who liked the current user
      const { data: likerProfile, error } = await supabase
        .from('profiles')
        .select('name')
        .eq('id', like.user_id)
        .single();

      if (error) throw error;

      // Show notification
      toast(
        <div className="flex items-center">
          <Heart className="h-5 w-5 text-red-500 mr-2" />
          <span>
            <strong>{likerProfile.name}</strong> liked your profile!
          </span>
        </div>,
        {
          action: {
            label: 'View',
            onClick: () => {
              // Navigate to matches page or specific profile
              window.location.href = '/matches';
            },
          },
        }
      );

      // Update unread count
      setUnreadCount(prev => prev + 1);
    } catch (error) {
      console.error('Error handling new like:', error);
    }
  };

  const handleNewMessage = async (message: any) => {
    try {
      // Get the sender's profile
      const { data: senderProfile, error } = await supabase
        .from('profiles')
        .select('name')
        .eq('id', message.sender_id)
        .single();

      if (error) throw error;

      // Show notification
      toast(
        <div className="flex items-center">
          <MessageCircle className="h-5 w-5 text-primary mr-2" />
          <div>
            <strong>{senderProfile.name}</strong>
            <p className="text-sm text-muted-foreground truncate">{message.content}</p>
          </div>
        </div>,
        {
          action: {
            label: 'Reply',
            onClick: () => {
              // Navigate to chat
              window.location.href = `/chat/${message.chat_room_id}`;
            },
          },
        }
      );

      // Update unread count
      setUnreadCount(prev => prev + 1);
    } catch (error) {
      console.error('Error handling new message:', error);
    }
  };

  if (!user || unreadCount === 0) return null;

  return (
    <div className="fixed top-4 right-4 z-50">
      <Badge variant="destructive" className="absolute -top-2 -right-2 h-5 w-5 flex items-center justify-center p-0">
        {unreadCount}
      </Badge>
      <Bell className="h-6 w-6 text-primary" />
    </div>
  );
};

export default NotificationSystem;
