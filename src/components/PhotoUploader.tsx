import React, { useState } from 'react';
import { supabase } from '@/lib/supabase';
import { Button } from '@/components/ui/button';
import { Camera, Trash2, Upload } from 'lucide-react';
import { toast } from 'sonner';

interface PhotoUploaderProps {
  userId: string;
  avatarUrl: string | null;
  onAvatarChange: (url: string) => void;
}

const PhotoUploader: React.FC<PhotoUploaderProps> = ({ userId, avatarUrl, onAvatarChange }) => {
  const [uploading, setUploading] = useState(false);
  const [preview, setPreview] = useState<string | null>(avatarUrl);

  const uploadAvatar = async (event: React.ChangeEvent<HTMLInputElement>) => {
    try {
      setUploading(true);

      if (!event.target.files || event.target.files.length === 0) {
        throw new Error('You must select an image to upload.');
      }

      const file = event.target.files[0];
      const fileExt = file.name.split('.').pop();
      const fileName = `${userId}-${Math.random()}.${fileExt}`;
      const filePath = `avatars/${fileName}`;

      // Upload the file to Supabase storage
      const { error: uploadError } = await supabase.storage
        .from('avatars')
        .upload(filePath, file);

      if (uploadError) {
        throw uploadError;
      }

      // Get the public URL
      const { data } = supabase.storage
        .from('avatars')
        .getPublicUrl(filePath);

      // Set the preview
      setPreview(data.publicUrl);
      
      // Call the callback with the new URL
      onAvatarChange(data.publicUrl);
      
      toast.success('Avatar uploaded successfully!');
    } catch (error) {
      console.error('Error uploading avatar:', error);
      toast.error('Error uploading avatar');
    } finally {
      setUploading(false);
    }
  };

  const removeAvatar = async () => {
    try {
      setUploading(true);
      
      // If there's no avatar, there's nothing to remove
      if (!preview) return;
      
      // Extract the file path from the URL
      const urlParts = preview.split('/');
      const fileName = urlParts[urlParts.length - 1];
      const filePath = `avatars/${fileName}`;
      
      // Remove the file from storage
      const { error } = await supabase.storage
        .from('avatars')
        .remove([filePath]);
        
      if (error) {
        throw error;
      }
      
      // Clear the preview
      setPreview(null);
      
      // Call the callback with an empty URL
      onAvatarChange('');
      
      toast.success('Avatar removed successfully!');
    } catch (error) {
      console.error('Error removing avatar:', error);
      toast.error('Error removing avatar');
    } finally {
      setUploading(false);
    }
  };

  return (
    <div className="flex flex-col items-center">
      <div className="relative w-32 h-32 mb-4">
        {preview ? (
          <img 
            src={preview} 
            alt="Avatar" 
            className="w-full h-full object-cover rounded-full"
          />
        ) : (
          <div className="w-full h-full rounded-full bg-muted flex items-center justify-center">
            <Camera className="h-10 w-10 text-muted-foreground" />
          </div>
        )}
        
        <div className="absolute -bottom-2 -right-2 flex gap-1">
          <label 
            htmlFor="avatar-upload" 
            className="bg-primary text-white p-2 rounded-full cursor-pointer hover:bg-primary/90"
          >
            <Upload className="h-4 w-4" />
            <input 
              id="avatar-upload" 
              type="file" 
              accept="image/*" 
              onChange={uploadAvatar} 
              disabled={uploading}
              className="hidden"
            />
          </label>
          
          {preview && (
            <Button
              size="icon"
              variant="destructive"
              className="h-8 w-8 rounded-full"
              onClick={removeAvatar}
              disabled={uploading}
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          )}
        </div>
      </div>
      
      <p className="text-sm text-muted-foreground text-center">
        {uploading ? 'Uploading...' : 'Upload your best photo to get more matches'}
      </p>
    </div>
  );
};

export default PhotoUploader;
