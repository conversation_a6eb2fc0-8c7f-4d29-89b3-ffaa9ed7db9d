
import { useDelayedAppear } from '@/lib/animations';
import { ScrollArea } from './ui/scroll-area';

type Message = {
  id: string;
  content: string;
  sender: 'user' | 'ai';
  timestamp: Date;
  showProfiles?: boolean;
};

type ConversationBubbleProps = {
  message: Message;
};

const ConversationBubble = ({ message }: ConversationBubbleProps) => {
  const isVisible = useDelayedAppear(100);
  
  const formatTime = (date: Date) => {
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };
  
  if (message.sender === 'user') {
    return (
      <div 
        className={`flex justify-end ${isVisible ? 'animate-fade-in' : 'opacity-0'}`}
      >
        <div className="max-w-[80%] md:max-w-[60%]">
          <div className="px-4 py-3 rounded-2xl rounded-tr-sm bg-primary text-primary-foreground font-sans">
            <p>{message.content}</p>
          </div>
          <div className="text-xs text-muted-foreground text-right mt-1 pr-1">
            {formatTime(message.timestamp)}
          </div>
        </div>
      </div>
    );
  }
  
  return (
    <div 
      className={`flex items-start ${isVisible ? 'animate-fade-in' : 'opacity-0'}`}
    >
      <div className="w-8 h-8 rounded-full bg-primary/20 flex items-center justify-center flex-shrink-0 mr-3">
        <span className="text-primary text-xs font-medium">AI</span>
      </div>
      <div className="max-w-[80%] md:max-w-[60%]">
        <div className="px-4 py-3 rounded-2xl rounded-tl-sm bg-secondary text-secondary-foreground font-sans">
          <p>{message.content}</p>
        </div>
        <div className="text-xs text-muted-foreground mt-1 pl-1">
          {formatTime(message.timestamp)}
        </div>
      </div>
    </div>
  );
};

export default ConversationBubble;
