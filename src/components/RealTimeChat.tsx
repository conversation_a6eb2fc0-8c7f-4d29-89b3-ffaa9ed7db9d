import React, { useState, useEffect, useRef } from 'react';
import { supabase } from '../lib/supabase';
import { User } from '@supabase/supabase-js';

interface ChatRoom {
  id: string;
  participants: string[];
  created_at: string;
  profiles: {
    id: string;
    name: string;
    avatar_url: string;
  }[];
}

interface Message {
  id: string;
  content: string;
  sender_id: string;
  created_at: string;
  profiles: {
    name: string;
    avatar_url: string;
  };
}

const RealTimeChat: React.FC = () => {
  const [user, setUser] = useState<User | null>(null);
  const [chatRooms, setChatRooms] = useState<ChatRoom[]>([]);
  const [selectedRoomId, setSelectedRoomId] = useState<string | null>(null);
  const [messages, setMessages] = useState<Message[]>([]);
  const [newMessage, setNewMessage] = useState('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    // Get the current user
    const getUser = async () => {
      const { data: { user } } = await supabase.auth.getUser();
      setUser(user);
    };

    getUser();
  }, []);

  useEffect(() => {
    // Fetch chat rooms when user is available
    if (user) {
      fetchChatRooms();
    }
  }, [user]);

  useEffect(() => {
    // Fetch messages when a room is selected
    if (selectedRoomId) {
      fetchMessages();
      
      // Subscribe to new messages
      const subscription = supabase
        .channel(`room:${selectedRoomId}`)
        .on('postgres_changes', { 
          event: 'INSERT', 
          schema: 'public', 
          table: 'messages',
          filter: `chat_room_id=eq.${selectedRoomId}`
        }, (payload) => {
          // When a new message is inserted, fetch the complete message with profile info
          fetchMessage(payload.new.id);
        })
        .subscribe();
      
      return () => {
        supabase.removeChannel(subscription);
      };
    }
  }, [selectedRoomId]);

  useEffect(() => {
    // Scroll to bottom when messages change
    scrollToBottom();
  }, [messages]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const fetchChatRooms = async () => {
    setLoading(true);
    setError(null);

    try {
      const { data: session } = await supabase.auth.getSession();
      const token = session?.session?.access_token;

      if (!token) {
        setError('You must be logged in to see chat rooms');
        setLoading(false);
        return;
      }

      const response = await fetch('https://chatnmatch.keithlam979.workers.dev/api/chats', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        throw new Error('Failed to fetch chat rooms');
      }

      const data = await response.json();
      setChatRooms(data.chatRooms || []);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  const fetchMessages = async () => {
    if (!selectedRoomId) return;

    setLoading(true);
    setError(null);

    try {
      const { data: session } = await supabase.auth.getSession();
      const token = session?.session?.access_token;

      if (!token) {
        setError('You must be logged in to see messages');
        setLoading(false);
        return;
      }

      const response = await fetch(`https://chatnmatch.keithlam979.workers.dev/api/chats/${selectedRoomId}/messages`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        throw new Error('Failed to fetch messages');
      }

      const data = await response.json();
      setMessages(data.messages || []);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  const fetchMessage = async (messageId: string) => {
    try {
      const { data: message, error: messageError } = await supabase
        .from('messages')
        .select('*, profiles(*)')
        .eq('id', messageId)
        .single();
      
      if (messageError) throw messageError;
      
      if (message) {
        setMessages(prev => [...prev, message as Message]);
      }
    } catch (err) {
      console.error('Error fetching new message:', err);
    }
  };

  const sendMessage = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!selectedRoomId || !newMessage.trim()) return;

    try {
      const { data: session } = await supabase.auth.getSession();
      const token = session?.session?.access_token;

      if (!token) {
        setError('You must be logged in to send messages');
        return;
      }

      const response = await fetch(`https://chatnmatch.keithlam979.workers.dev/api/chats/${selectedRoomId}/messages`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ content: newMessage })
      });

      if (!response.ok) {
        throw new Error('Failed to send message');
      }

      // Clear the input field
      setNewMessage('');
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    }
  };

  const getOtherParticipant = (room: ChatRoom) => {
    if (!user) return null;
    const otherParticipantId = room.participants.find(id => id !== user.id);
    return room.profiles.find(profile => profile.id === otherParticipantId);
  };

  if (loading && !selectedRoomId) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="flex items-center gap-2">
          <div className="h-3 w-3 bg-primary rounded-full animate-pulse"></div>
          <div className="h-3 w-3 bg-primary rounded-full animate-pulse animate-delay-200"></div>
          <div className="h-3 w-3 bg-primary rounded-full animate-pulse animate-delay-400"></div>
        </div>
      </div>
    );
  }

  if (error && !selectedRoomId) {
    return (
      <div className="flex flex-col items-center justify-center h-full p-4">
        <p className="text-red-500 mb-4">{error}</p>
        <button 
          onClick={fetchChatRooms}
          className="px-4 py-2 bg-primary text-white rounded-full hover:bg-primary/80"
        >
          Try Again
        </button>
      </div>
    );
  }

  if (!user) {
    return (
      <div className="flex items-center justify-center h-full p-4">
        <p className="text-center">Please log in to see your chats</p>
      </div>
    );
  }

  return (
    <div className="flex h-full">
      <div className="w-1/3 border-r bg-muted/20 overflow-y-auto">
        <div className="p-4 border-b">
          <h2 className="text-xl font-semibold">Your Matches</h2>
        </div>
        
        {chatRooms.length === 0 ? (
          <div className="p-4 text-center text-muted-foreground">
            No matches yet. Start liking profiles!
          </div>
        ) : (
          <div className="divide-y">
            {chatRooms.map(room => {
              const otherParticipant = getOtherParticipant(room);
              return (
                <div 
                  key={room.id} 
                  className={`p-4 cursor-pointer hover:bg-muted/30 transition-colors ${selectedRoomId === room.id ? 'bg-muted/40' : ''}`}
                  onClick={() => setSelectedRoomId(room.id)}
                >
                  <div className="flex items-center gap-3">
                    <img 
                      src={otherParticipant?.avatar_url || 'https://via.placeholder.com/40'} 
                      alt={otherParticipant?.name} 
                      className="w-12 h-12 rounded-full object-cover"
                    />
                    <div>
                      <h3 className="font-medium">{otherParticipant?.name}</h3>
                      <p className="text-sm text-muted-foreground">
                        {new Date(room.created_at).toLocaleDateString()}
                      </p>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </div>
      
      <div className="flex-1 flex flex-col">
        {selectedRoomId ? (
          <>
            <div className="p-4 border-b bg-card">
              {chatRooms.map(room => {
                if (room.id === selectedRoomId) {
                  const otherParticipant = getOtherParticipant(room);
                  return (
                    <div key={room.id} className="flex items-center gap-3">
                      <img 
                        src={otherParticipant?.avatar_url || 'https://via.placeholder.com/40'} 
                        alt={otherParticipant?.name} 
                        className="w-10 h-10 rounded-full object-cover"
                      />
                      <h2 className="font-semibold">{otherParticipant?.name}</h2>
                    </div>
                  );
                }
                return null;
              })}
            </div>
            
            <div className="flex-1 overflow-y-auto p-4">
              {loading ? (
                <div className="flex justify-center p-4">
                  <div className="flex items-center gap-2">
                    <div className="h-2 w-2 bg-primary rounded-full animate-pulse"></div>
                    <div className="h-2 w-2 bg-primary rounded-full animate-pulse animate-delay-200"></div>
                    <div className="h-2 w-2 bg-primary rounded-full animate-pulse animate-delay-400"></div>
                  </div>
                </div>
              ) : messages.length === 0 ? (
                <div className="flex justify-center items-center h-full text-muted-foreground">
                  No messages yet. Say hello!
                </div>
              ) : (
                <div className="space-y-4">
                  {messages.map(message => (
                    <div 
                      key={message.id} 
                      className={`flex ${message.sender_id === user.id ? 'justify-end' : 'justify-start'}`}
                    >
                      {message.sender_id !== user.id && (
                        <img 
                          src={message.profiles.avatar_url || 'https://via.placeholder.com/30'} 
                          alt={message.profiles.name} 
                          className="w-8 h-8 rounded-full mr-2 self-end"
                        />
                      )}
                      <div 
                        className={`max-w-[70%] p-3 rounded-lg ${
                          message.sender_id === user.id 
                            ? 'bg-primary text-primary-foreground rounded-br-none' 
                            : 'bg-muted rounded-bl-none'
                        }`}
                      >
                        <p>{message.content}</p>
                        <p className={`text-xs mt-1 ${
                          message.sender_id === user.id 
                            ? 'text-primary-foreground/70' 
                            : 'text-muted-foreground'
                        }`}>
                          {new Date(message.created_at).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                        </p>
                      </div>
                    </div>
                  ))}
                  <div ref={messagesEndRef} />
                </div>
              )}
            </div>
            
            <form className="p-4 border-t bg-card" onSubmit={sendMessage}>
              <div className="flex gap-2">
                <input
                  type="text"
                  value={newMessage}
                  onChange={(e) => setNewMessage(e.target.value)}
                  placeholder="Type a message..."
                  className="flex-1 px-4 py-2 rounded-full border focus:outline-none focus:ring-2 focus:ring-primary"
                  disabled={loading}
                />
                <button 
                  type="submit" 
                  disabled={loading || !newMessage.trim()}
                  className="px-4 py-2 bg-primary text-white rounded-full disabled:opacity-50"
                >
                  Send
                </button>
              </div>
            </form>
          </>
        ) : (
          <div className="flex items-center justify-center h-full text-muted-foreground">
            Select a chat to start messaging
          </div>
        )}
      </div>
    </div>
  );
};

export default RealTimeChat;
