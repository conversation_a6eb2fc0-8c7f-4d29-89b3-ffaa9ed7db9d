
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { useDelayedAppear } from '@/lib/animations';
import { ChevronRight, Heart } from 'lucide-react';

const WelcomeScreen = () => {
  const navigate = useNavigate();
  const isVisible = useDelayedAppear(300);

  return (
    <div className="flex flex-col items-center justify-center w-full min-h-screen px-6 py-12 overflow-hidden">
      <div
        className={`flex flex-col items-center max-w-lg text-center space-y-6 ${
          isVisible ? 'animate-fade-up' : 'opacity-0'
        }`}
      >
        <div className="relative p-4">
          <div className="absolute inset-0 bg-primary/10 rounded-full blur-2xl opacity-20"></div>
          <Heart className="w-16 h-16 text-primary animate-pulse animate-duration-1000" />
        </div>

        <h1 className="text-4xl font-medium tracking-tight md:text-5xl">
          <span className="text-gradient">
            chatnmatch
          </span>
        </h1>

        <p className="text-lg text-muted-foreground">
          Where meaningful conversations lead to meaningful connections
        </p>

        <div className="w-full max-w-xs pt-6">
          <Button
            className="w-full h-12 text-base transition-all duration-300 rounded-full bg-primary hover:bg-primary/90 text-white"
            onClick={() => navigate('/auth')}
          >
            Get Started <ChevronRight className="ml-2 w-4 h-4" />
          </Button>

          <div className="flex items-center justify-center mt-4">
            <Button
              variant="link"
              className="text-muted-foreground hover:text-foreground"
              onClick={() => navigate('/auth')}
            >
              Login / Sign Up
            </Button>
          </div>
        </div>
      </div>

      <div className="fixed bottom-0 left-0 right-0 flex justify-center p-6">
        <p className="text-xs text-muted-foreground">
          By continuing, you agree to our Terms of Service and Privacy Policy
        </p>
      </div>
    </div>
  );
};

export default WelcomeScreen;
