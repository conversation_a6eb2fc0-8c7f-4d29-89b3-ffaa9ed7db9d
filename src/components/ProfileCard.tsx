
import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { MessageCircle, Heart, ChevronDown, ChevronUp } from 'lucide-react';
import { useDelayedAppear } from '@/lib/animations';

type Profile = {
  id: string;
  name: string;
  age: number;
  location: string;
  bio: string;
  interests: string[];
  imageUrl: string;
  isLiked?: boolean;
};

type ProfileCardProps = {
  profile: Profile;
  onLike?: () => void;
  onChat?: (profile: Profile) => void;
};

const ProfileCard = ({ profile, onLike, onChat }: ProfileCardProps) => {
  const [expanded, setExpanded] = useState(false);
  const isVisible = useDelayedAppear(200);

  return (
    <div
      className={`w-64 rounded-xl overflow-hidden bg-card border border-border shadow-soft ${
        isVisible ? 'animate-scale-in' : 'opacity-0'
      }`}
    >
      <div className="aspect-[4/5] overflow-hidden">
        <img
          src={profile.imageUrl}
          alt={profile.name}
          className="w-full h-full object-cover transition-transform duration-300 hover:scale-105"
          loading="lazy"
        />
      </div>

      <div className="p-4">
        <div className="flex justify-between items-start">
          <h3 className="text-xl font-medium leading-tight">
            {profile.name}, {profile.age}
          </h3>
          <Button
            variant="ghost"
            size="sm"
            className="p-1 h-auto"
            onClick={() => setExpanded(!expanded)}
          >
            {expanded ?
              <ChevronUp className="h-4 w-4 text-muted-foreground" /> :
              <ChevronDown className="h-4 w-4 text-muted-foreground" />
            }
          </Button>
        </div>

        <p className="text-sm text-muted-foreground mt-1">
          {profile.location}
        </p>

        {expanded && (
          <div className="mt-3 space-y-2">
            <p className="text-sm">{profile.bio}</p>

            <div className="flex flex-wrap gap-1 mt-2">
              {profile.interests.map((interest) => (
                <span
                  key={interest}
                  className="text-xs px-2 py-1 rounded-full bg-secondary text-secondary-foreground"
                >
                  {interest}
                </span>
              ))}
            </div>
          </div>
        )}

        <div className="flex gap-2 mt-4">
          <Button
            className={`flex-1 h-9 rounded-full ${profile.isLiked
              ? 'bg-primary/20 text-primary border border-primary'
              : 'bg-primary hover:bg-primary/80 text-white'}`}
            onClick={onLike}
          >
            <Heart className={`h-4 w-4 mr-1 ${profile.isLiked ? 'fill-primary' : ''}`} />
            {profile.isLiked ? 'Liked' : 'Like'}
          </Button>

          <Button
            variant="outline"
            className="flex-1 h-9 rounded-full border-border hover:bg-secondary"
            onClick={() => onChat && onChat(profile)}
          >
            <MessageCircle className="h-4 w-4 mr-1" />
            Chat
          </Button>
        </div>
      </div>
    </div>
  );
};

export default ProfileCard;
