import React, { useState, useEffect } from 'react';
import { supabase } from '../lib/supabase';
import { User } from '@supabase/supabase-js';

interface Profile {
  id: string;
  name: string;
  bio: string;
  avatar_url: string;
  interests: string[];
}

const MatchingInterface: React.FC = () => {
  const [user, setUser] = useState<User | null>(null);
  const [potentialMatches, setPotentialMatches] = useState<Profile[]>([]);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    // Get the current user
    const getUser = async () => {
      const { data: { user } } = await supabase.auth.getUser();
      setUser(user);
    };

    getUser();
  }, []);

  useEffect(() => {
    // Fetch potential matches when user is available
    if (user) {
      fetchPotentialMatches();
    }
  }, [user]);

  const fetchPotentialMatches = async () => {
    setLoading(true);
    setError(null);

    try {
      const { data: session } = await supabase.auth.getSession();
      const token = session?.session?.access_token;

      if (!token) {
        setError('You must be logged in to see matches');
        setLoading(false);
        return;
      }

      const response = await fetch('https://chatnmatch.keithlam979.workers.dev/api/matches', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        throw new Error('Failed to fetch matches');
      }

      const data = await response.json();
      setPotentialMatches(data.matches || []);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  const handleLike = async () => {
    if (!user || currentIndex >= potentialMatches.length) return;

    try {
      const { data: session } = await supabase.auth.getSession();
      const token = session?.session?.access_token;

      if (!token) {
        setError('You must be logged in to like profiles');
        return;
      }

      const profileId = potentialMatches[currentIndex].id;

      const response = await fetch('https://chatnmatch.keithlam979.workers.dev/api/like', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ profileId })
      });

      if (!response.ok) {
        throw new Error('Failed to like profile');
      }

      const data = await response.json();
      
      // If it's a match, show a notification
      if (data.isMatch) {
        alert(`It's a match with ${potentialMatches[currentIndex].name}!`);
      }

      // Move to the next profile
      setCurrentIndex(prev => prev + 1);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    }
  };

  const handleDislike = () => {
    // Simply move to the next profile
    setCurrentIndex(prev => prev + 1);
  };

  // If we've gone through all potential matches
  if (currentIndex >= potentialMatches.length && potentialMatches.length > 0) {
    return (
      <div className="flex flex-col items-center justify-center h-full p-4">
        <h2 className="text-xl font-semibold mb-4">No more profiles to show</h2>
        <button 
          onClick={fetchPotentialMatches}
          className="px-4 py-2 bg-primary text-white rounded-full hover:bg-primary/80"
        >
          Refresh
        </button>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="flex items-center gap-2">
          <div className="h-3 w-3 bg-primary rounded-full animate-pulse"></div>
          <div className="h-3 w-3 bg-primary rounded-full animate-pulse animate-delay-200"></div>
          <div className="h-3 w-3 bg-primary rounded-full animate-pulse animate-delay-400"></div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center h-full p-4">
        <p className="text-red-500 mb-4">{error}</p>
        <button 
          onClick={fetchPotentialMatches}
          className="px-4 py-2 bg-primary text-white rounded-full hover:bg-primary/80"
        >
          Try Again
        </button>
      </div>
    );
  }

  if (!user) {
    return (
      <div className="flex items-center justify-center h-full p-4">
        <p className="text-center">Please log in to see potential matches</p>
      </div>
    );
  }

  if (potentialMatches.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center h-full p-4">
        <h2 className="text-xl font-semibold mb-4">No potential matches found</h2>
        <button 
          onClick={fetchPotentialMatches}
          className="px-4 py-2 bg-primary text-white rounded-full hover:bg-primary/80"
        >
          Refresh
        </button>
      </div>
    );
  }

  const currentProfile = potentialMatches[currentIndex];

  return (
    <div className="flex flex-col items-center justify-center h-full p-4">
      <div className="w-full max-w-md bg-card rounded-xl overflow-hidden shadow-lg">
        <div className="relative">
          <img 
            src={currentProfile.avatar_url || 'https://via.placeholder.com/400x300'} 
            alt={currentProfile.name} 
            className="w-full h-64 object-cover"
          />
          <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-4">
            <h2 className="text-xl font-bold text-white">{currentProfile.name}</h2>
          </div>
        </div>
        
        <div className="p-4">
          <p className="text-muted-foreground mb-4">{currentProfile.bio}</p>
          
          <div className="flex flex-wrap gap-2 mb-6">
            {currentProfile.interests?.map((interest, index) => (
              <span 
                key={index} 
                className="px-3 py-1 bg-primary/10 text-primary rounded-full text-sm"
              >
                {interest}
              </span>
            ))}
          </div>
          
          <div className="flex justify-between gap-4 mt-4">
            <button 
              onClick={handleDislike}
              className="flex-1 py-3 bg-destructive/10 text-destructive font-medium rounded-lg hover:bg-destructive/20"
            >
              Dislike
            </button>
            <button 
              onClick={handleLike}
              className="flex-1 py-3 bg-primary text-white font-medium rounded-lg hover:bg-primary/80"
            >
              Like
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MatchingInterface;
