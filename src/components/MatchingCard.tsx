import React, { useState, useEffect } from 'react';
import { supabase } from '../lib/supabase';
import { User } from '@supabase/supabase-js';

interface Profile {
  id: string;
  name: string;
  bio: string;
  avatar_url: string;
  interests: string[];
}

const MatchingCard: React.FC = () => {
  const [user, setUser] = useState<User | null>(null);
  const [potentialMatches, setPotentialMatches] = useState<Profile[]>([]);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    // Get the current user
    const getUser = async () => {
      const { data: { user } } = await supabase.auth.getUser();
      setUser(user);
    };

    getUser();
  }, []);

  useEffect(() => {
    // Fetch potential matches when user is available
    if (user) {
      fetchPotentialMatches();
    }
  }, [user]);

  const fetchPotentialMatches = async () => {
    setLoading(true);
    setError(null);

    try {
      const { data: session } = await supabase.auth.getSession();
      const token = session?.session?.access_token;

      if (!token) {
        setError('You must be logged in to see matches');
        setLoading(false);
        return;
      }

      const response = await fetch('https://chatnmatch.keithlam979.workers.dev/api/matches', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        throw new Error('Failed to fetch matches');
      }

      const data = await response.json();
      setPotentialMatches(data.matches || []);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  const handleLike = async () => {
    if (!user || currentIndex >= potentialMatches.length) return;

    try {
      const { data: session } = await supabase.auth.getSession();
      const token = session?.session?.access_token;

      if (!token) {
        setError('You must be logged in to like profiles');
        return;
      }

      const profileId = potentialMatches[currentIndex].id;

      const response = await fetch('https://chatnmatch.keithlam979.workers.dev/api/like', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ profileId })
      });

      if (!response.ok) {
        throw new Error('Failed to like profile');
      }

      const data = await response.json();
      
      // If it's a match, show a notification
      if (data.isMatch) {
        alert(`It's a match with ${potentialMatches[currentIndex].name}!`);
      }

      // Move to the next profile
      setCurrentIndex(prev => prev + 1);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    }
  };

  const handleDislike = () => {
    // Simply move to the next profile
    setCurrentIndex(prev => prev + 1);
  };

  // If we've gone through all potential matches
  if (currentIndex >= potentialMatches.length && potentialMatches.length > 0) {
    return (
      <div className="matching-card">
        <h2>No more profiles to show</h2>
        <button onClick={fetchPotentialMatches}>Refresh</button>
      </div>
    );
  }

  if (loading) {
    return <div className="matching-card">Loading...</div>;
  }

  if (error) {
    return (
      <div className="matching-card">
        <p className="error">{error}</p>
        <button onClick={fetchPotentialMatches}>Try Again</button>
      </div>
    );
  }

  if (!user) {
    return (
      <div className="matching-card">
        <p>Please log in to see potential matches</p>
      </div>
    );
  }

  if (potentialMatches.length === 0) {
    return (
      <div className="matching-card">
        <h2>No potential matches found</h2>
        <button onClick={fetchPotentialMatches}>Refresh</button>
      </div>
    );
  }

  const currentProfile = potentialMatches[currentIndex];

  return (
    <div className="matching-card">
      <div className="profile-card">
        <img 
          src={currentProfile.avatar_url || 'https://via.placeholder.com/150'} 
          alt={currentProfile.name} 
          className="profile-image"
        />
        <h2>{currentProfile.name}</h2>
        <p>{currentProfile.bio}</p>
        <div className="interests">
          {currentProfile.interests?.map((interest, index) => (
            <span key={index} className="interest-tag">{interest}</span>
          ))}
        </div>
      </div>
      <div className="action-buttons">
        <button onClick={handleDislike} className="dislike-button">Dislike</button>
        <button onClick={handleLike} className="like-button">Like</button>
      </div>
    </div>
  );
};

export default MatchingCard;
