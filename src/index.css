@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 240 10% 3.9%;

    --card: 0 0% 100%;
    --card-foreground: 240 10% 3.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;

    --primary: 280 70% 35%;
    --primary-foreground: 0 0% 100%;

    --secondary: 240 5% 96%;
    --secondary-foreground: 240 6% 10%;

    --muted: 240 5% 96%;
    --muted-foreground: 240 4% 46%;

    --accent: 280 50% 95%;
    --accent-foreground: 280 60% 40%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 98%;

    --border: 240 6% 90%;
    --input: 240 6% 90%;
    --ring: 280 60% 30%;

    --radius: 1rem;

    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5% 26%;
    --sidebar-primary: 240 6% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 5% 96%;
    --sidebar-accent-foreground: 240 6% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217 92% 60%;
  }

  .dark {
    --background: 0 0% 0%;
    --foreground: 0 0% 98%;

    --card: 0 0% 3%;
    --card-foreground: 0 0% 98%;

    --popover: 0 0% 3%;
    --popover-foreground: 0 0% 98%;

    --primary: 280 73% 35%;
    --primary-foreground: 0 0% 100%;

    --secondary: 240 4% 10%;
    --secondary-foreground: 0 0% 98%;

    --muted: 240 4% 16%;
    --muted-foreground: 240 5% 65%;

    --accent: 280 15% 25%;
    --accent-foreground: 0 0% 98%;

    --destructive: 0 63% 31%;
    --destructive-foreground: 0 0% 98%;

    --border: 280 10% 20%;
    --input: 280 10% 20%;
    --ring: 280 50% 60%;

    --sidebar-background: 0 0% 10%;
    --sidebar-foreground: 240 5% 96%;
    --sidebar-primary: 280 50% 60%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 280 10% 20%;
    --sidebar-accent-foreground: 240 5% 96%;
    --sidebar-border: 280 10% 20%;
    --sidebar-ring: 280 50% 60%;
  }
}

@font-face {
  font-family: 'GTAmerica';
  src: url('https://assets.hinge.co/fonts/GT-America-Standard-Regular.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'GTAmerica';
  src: url('https://assets.hinge.co/fonts/GT-America-Standard-Medium.woff2') format('woff2');
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'GTAmerica';
  src: url('https://assets.hinge.co/fonts/GT-America-Standard-Bold.woff2') format('woff2');
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}

@layer base {
  * {
    @apply border-border;
  }

  html, body {
    height: 100%;
    overflow-x: hidden;
    font-family: 'GTAmerica', -apple-system, BlinkMacSystemFont, sans-serif;
  }

  body {
    @apply bg-background text-foreground antialiased;
    font-feature-settings: "ss01", "ss02", "cv01", "cv02";
  }

  #root {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    width: 100%;
  }

  h1, h2, h3, h4, h5, h6 {
    font-family: 'GTAmerica', -apple-system, BlinkMacSystemFont, sans-serif;
    font-weight: 500;
  }
}

@layer utilities {
  .glass {
    @apply backdrop-blur-xl bg-white/80 dark:bg-black/30 border border-white/20 dark:border-white/10;
  }

  .text-balance {
    text-wrap: balance;
  }

  .text-gradient {
    @apply bg-clip-text text-transparent bg-gradient-to-r from-primary to-purple-700;
  }

  .safe-bottom {
    padding-bottom: env(safe-area-inset-bottom);
  }

  .safe-top {
    padding-top: env(safe-area-inset-top);
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  @apply bg-muted-foreground/30 rounded-full;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-muted-foreground/50;
}

/* Animation classes */
.animate-delay-100 {
  animation-delay: 100ms;
}

.animate-delay-200 {
  animation-delay: 200ms;
}

.animate-delay-300 {
  animation-delay: 300ms;
}

.animate-delay-400 {
  animation-delay: 400ms;
}

.animate-delay-500 {
  animation-delay: 500ms;
}

.animate-duration-300 {
  animation-duration: 300ms;
}

.animate-duration-500 {
  animation-duration: 500ms;
}

.animate-duration-700 {
  animation-duration: 700ms;
}

.animate-duration-1000 {
  animation-duration: 1000ms;
}
