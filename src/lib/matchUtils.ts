
/**
 * Calculates the probability of a match based on profile compatibility
 * @param profileA - First profile interests
 * @param profileB - Second profile or user interests
 * @returns A number between 0 and 1 representing match probability
 */
export const calculateMatchProbability = (
  profileAInterests: string[], 
  profileBInterests: string[]
): number => {
  // If no interests, return random low probability
  if (!profileAInterests.length || !profileBInterests.length) {
    return Math.random() * 0.3;
  }
  
  // Count matching interests
  const matchingInterests = profileAInterests.filter(interest => 
    profileBInterests.includes(interest)
  ).length;
  
  // Calculate base probability
  let probability = matchingInterests / Math.max(profileAInterests.length, profileBInterests.length);
  
  // Add some randomness to make it more realistic
  probability = probability * 0.7 + Math.random() * 0.3;
  
  // Cap at 0.9 to avoid too many automatic matches
  return Math.min(probability, 0.9);
};

/**
 * Simulates whether a match would occur based on probability
 * @param probability - The match probability between 0 and 1
 * @returns Boolean indicating whether a match occurred
 */
export const simulateMatch = (probability: number): boolean => {
  return Math.random() < probability;
};

// Mock user interests for matching
export const userInterests = [
  'Travel',
  'Photography',
  'Music',
  'Cooking',
  'Technology'
];
