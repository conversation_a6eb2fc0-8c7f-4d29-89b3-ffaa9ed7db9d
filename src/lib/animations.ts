
import { useEffect, useState } from 'react';

// Use this hook to delay mounting a component for animations
export function useDelayedAppear(delay = 300): boolean {
  const [isVisible, setIsVisible] = useState(false);
  
  useEffect(() => {
    const timeout = setTimeout(() => {
      setIsVisible(true);
    }, delay);
    
    return () => clearTimeout(timeout);
  }, [delay]);
  
  return isVisible;
}

// Use this to stagger animations of multiple elements
export function useStaggeredChildren(total: number, baseDelay = 100): number[] {
  return Array.from({ length: total }, (_, i) => (i + 1) * baseDelay);
}

// Animate in an element when it's in the viewport
export function useIntersectionAnimation(
  ref: React.RefObject<HTMLElement>,
  animationClass: string = 'animate-fade-in',
  options = {}
): boolean {
  const [isIntersecting, setIsIntersecting] = useState(false);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsIntersecting(true);
          if (ref.current) {
            ref.current.classList.add(animationClass);
          }
          observer.disconnect();
        }
      },
      { threshold: 0.1, ...options }
    );

    if (ref.current) {
      observer.observe(ref.current);
    }

    return () => {
      observer.disconnect();
    };
  }, [ref, animationClass, options]);

  return isIntersecting;
}
