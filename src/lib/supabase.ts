import { createClient } from '@supabase/supabase-js';

// Get Supabase credentials from window variables (set by env-config.js)
// Otherwise fall back to hardcoded values for local development
const supabaseUrl = typeof window !== 'undefined' && window.SUPABASE_URL
  ? window.SUPABASE_URL
  : 'https://dknbfjkoiozntvsaguwh.supabase.co';

const supabaseAnonKey = typeof window !== 'undefined' && window.SUPABASE_ANON_KEY
  ? window.SUPABASE_ANON_KEY
  : 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImRrbmJmamtvaW96bnR2c2FndXdoIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDUzOTc0ODgsImV4cCI6MjA2MDk3MzQ4OH0.HH6OhrFWJzZhXapYDDa0xXujV9p9IQwG5hRkm9UCpzs';

// Create a Supabase client
export const supabase = createClient(supabaseUrl, supabaseAnonKey);

// API helper for Worker endpoints
export const api = {
  async get(endpoint: string) {
    const { data: session } = await supabase.auth.getSession();
    const token = session?.session?.access_token;

    const response = await fetch(`https://chatnmatch.keithlam979.workers.dev/api/${endpoint}`, {
      headers: {
        'Authorization': token ? `Bearer ${token}` : ''
      }
    });

    if (!response.ok) {
      throw new Error(`API error: ${response.statusText}`);
    }

    return response.json();
  },

  async post(endpoint: string, data: any) {
    const { data: session } = await supabase.auth.getSession();
    const token = session?.session?.access_token;

    const response = await fetch(`https://chatnmatch.keithlam979.workers.dev/api/${endpoint}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': token ? `Bearer ${token}` : ''
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      throw new Error(`API error: ${response.statusText}`);
    }

    return response.json();
  },

  async put(endpoint: string, data: any) {
    const { data: session } = await supabase.auth.getSession();
    const token = session?.session?.access_token;

    const response = await fetch(`https://chatnmatch.keithlam979.workers.dev/api/${endpoint}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': token ? `Bearer ${token}` : ''
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      throw new Error(`API error: ${response.statusText}`);
    }

    return response.json();
  },

  async delete(endpoint: string) {
    const { data: session } = await supabase.auth.getSession();
    const token = session?.session?.access_token;

    const response = await fetch(`https://chatnmatch.keithlam979.workers.dev/api/${endpoint}`, {
      method: 'DELETE',
      headers: {
        'Authorization': token ? `Bearer ${token}` : ''
      },
    });

    if (!response.ok) {
      throw new Error(`API error: ${response.statusText}`);
    }

    return response.json();
  },
};

// Types for our database tables
export type Profile = {
  id: string;
  created_at?: string;
  name: string;
  age: number;
  gender: string;
  interested_in: string;
  location: string;
  bio: string;
  occupation?: string;
  education?: string;
  height?: number;
  interests: string[];
  avatar_url: string;
};

export type Match = {
  id: string;
  created_at?: string;
  user_id: string;
  matched_user_id: string;
  is_mutual: boolean;
};

export type Message = {
  id: string;
  created_at?: string;
  sender_id: string;
  receiver_id: string;
  content: string;
  is_read: boolean;
};

// Auth functions
export const signUp = async (email: string, password: string) => {
  const { data, error } = await supabase.auth.signUp({
    email,
    password,
  });
  return { data, error };
};

export const signIn = async (email: string, password: string) => {
  const { data, error } = await supabase.auth.signInWithPassword({
    email,
    password,
  });
  return { data, error };
};

export const signOut = async () => {
  const { error } = await supabase.auth.signOut();
  return { error };
};

export const getCurrentUser = async () => {
  const { data, error } = await supabase.auth.getUser();
  return { data, error };
};

// Profile functions
export const getProfile = async (userId: string) => {
  const { data, error } = await supabase
    .from('profiles')
    .select('*')
    .eq('id', userId)
    .single();
  return { data, error };
};

export const updateProfile = async (userId: string, profile: Partial<Profile>) => {
  const { data, error } = await supabase
    .from('profiles')
    .update(profile)
    .eq('id', userId);
  return { data, error };
};

export const createProfile = async (profile: Partial<Profile>) => {
  const { data, error } = await supabase
    .from('profiles')
    .insert([profile]);
  return { data, error };
};

// Match functions
export const likeProfile = async (userId: string, likedUserId: string) => {
  // Check if the other user has already liked this user
  const { data: existingMatch } = await supabase
    .from('matches')
    .select('*')
    .eq('user_id', likedUserId)
    .eq('matched_user_id', userId)
    .single();

  const isMutual = !!existingMatch;

  // Create a new match
  const { data, error } = await supabase
    .from('matches')
    .insert([
      {
        user_id: userId,
        matched_user_id: likedUserId,
        is_mutual: isMutual,
      },
    ]);

  // If this is a mutual match, update the other match as well
  if (isMutual) {
    await supabase
      .from('matches')
      .update({ is_mutual: true })
      .eq('user_id', likedUserId)
      .eq('matched_user_id', userId);
  }

  return { data, error, isMutual };
};

export const getMatches = async (userId: string) => {
  const { data, error } = await supabase
    .from('matches')
    .select(`
      id,
      created_at,
      matched_user_id,
      is_mutual,
      profiles:matched_user_id (*)
    `)
    .eq('user_id', userId)
    .eq('is_mutual', true);
  return { data, error };
};

export const getPotentialMatches = async (userId: string, limit = 10) => {
  // Get user's preferences
  const { data: userProfile } = await getProfile(userId);

  if (!userProfile) {
    return { data: null, error: new Error('User profile not found') };
  }

  // Get users that match the user's preferences
  const { data, error } = await supabase
    .from('profiles')
    .select('*')
    .neq('id', userId)
    .eq('gender', userProfile.interested_in)
    .eq('interested_in', userProfile.gender)
    .limit(limit);

  return { data, error };
};

// Message functions
export const sendMessage = async (senderId: string, receiverId: string, content: string) => {
  const { data, error } = await supabase
    .from('messages')
    .insert([
      {
        sender_id: senderId,
        receiver_id: receiverId,
        content,
        is_read: false,
      },
    ]);
  return { data, error };
};

export const getMessages = async (userId: string, otherUserId: string) => {
  const { data, error } = await supabase
    .from('messages')
    .select('*')
    .or(`sender_id.eq.${userId},receiver_id.eq.${userId}`)
    .or(`sender_id.eq.${otherUserId},receiver_id.eq.${otherUserId}`)
    .order('created_at', { ascending: true });
  return { data, error };
};

export const getConversations = async (userId: string) => {
  // Get all messages where the user is either sender or receiver
  const { data: messages, error } = await supabase
    .from('messages')
    .select('*')
    .or(`sender_id.eq.${userId},receiver_id.eq.${userId}`)
    .order('created_at', { ascending: false });

  if (error || !messages) {
    return { data: null, error };
  }

  // Get unique conversation partners
  const conversationPartners = new Set<string>();
  const conversations: any[] = [];

  messages.forEach(message => {
    const partnerId = message.sender_id === userId ? message.receiver_id : message.sender_id;

    if (!conversationPartners.has(partnerId)) {
      conversationPartners.add(partnerId);
      conversations.push({
        partnerId,
        lastMessage: message.content,
        timestamp: message.created_at,
        isRead: message.is_read,
        isFromUser: message.sender_id === userId,
      });
    }
  });

  // Get profile information for each conversation partner
  const conversationsWithProfiles = await Promise.all(
    conversations.map(async (conversation) => {
      const { data: profile } = await getProfile(conversation.partnerId);
      return {
        ...conversation,
        profile,
      };
    })
  );

  return { data: conversationsWithProfiles, error: null };
};

// Storage functions
export const uploadAvatar = async (userId: string, file: File) => {
  const fileExt = file.name.split('.').pop();
  const fileName = `${userId}-${Math.random()}.${fileExt}`;
  const filePath = `avatars/${fileName}`;

  const { data, error } = await supabase.storage
    .from('avatars')
    .upload(filePath, file);

  if (error) {
    return { data: null, error };
  }

  // Get the public URL
  const { data: publicURL } = supabase.storage
    .from('avatars')
    .getPublicUrl(filePath);

  // Update the user's profile with the new avatar URL
  await updateProfile(userId, {
    avatar_url: publicURL.publicUrl,
  });

  return { data: publicURL, error: null };
};
