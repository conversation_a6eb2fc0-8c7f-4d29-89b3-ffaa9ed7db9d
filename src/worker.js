export default {
  async fetch(request, env, ctx) {
    // Get the URL from the request
    const url = new URL(request.url);
    
    // Log for debugging
    console.log("Worker received request for:", url.pathname);
    
    // Handle API requests
    if (url.pathname.startsWith('/api/')) {
      return new Response(JSON.stringify({ 
        message: 'API endpoint',
        path: url.pathname
      }), {
        headers: { 'Content-Type': 'application/json' }
      });
    }
    
    // Return a simple HTML response for testing
    return new Response(`
      <!DOCTYPE html>
      <html>
        <head>
          <title>ChatNMatch</title>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
        </head>
        <body>
          <h1>ChatNMatch is working!</h1>
          <p>This is a simple test page to verify the worker is running correctly.</p>
          <p>Path: ${url.pathname}</p>
          <p>Supabase URL: ${env.SUPABASE_URL || 'Not set'}</p>
          <p>Supabase Key Available: ${env.SUPABASE_ANON_KEY ? 'Yes' : 'No'}</p>
        </body>
      </html>
    `, {
      headers: { 'Content-Type': 'text/html' }
    });
  }
};
