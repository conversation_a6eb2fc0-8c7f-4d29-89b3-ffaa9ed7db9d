export async function onRequest(context) {
  const { request, env, next } = context;
  const url = new URL(request.url);

  // Handle API requests
  if (url.pathname.startsWith('/api/')) {
    return new Response(JSON.stringify({ message: 'API endpoint' }), {
      headers: { 'Content-Type': 'application/json' }
    });
  }

  // Handle environment variable requests
  if (url.pathname === '/env-config.js') {
    // Access secrets from env
    const supabaseUrl = env.SUPABASE_URL || 'https://dknbfjkoiozntvsaguwh.supabase.co';
    const supabaseAnonKey = env.SUPABASE_ANON_KEY || '';
    
    console.log('Supabase URL:', supabaseUrl);
    console.log('Supabase Anon Key available:', !!supabaseAnonKey);
    
    return new Response(
      `window.SUPABASE_URL = "${supabaseUrl}";
       window.SUPABASE_ANON_KEY = "${supabaseAnonKey}";
      `,
      {
        headers: { 'Content-Type': 'application/javascript' }
      }
    );
  }

  // Process the request with the next handler
  const response = await next();

  // If this is an HTML response, inject our environment variables script
  const contentType = response.headers.get('content-type') || '';
  if (contentType.includes('text/html')) {
    let html = await response.text();
    const scriptTag = `<script src="/env-config.js"></script>`;
    
    // Insert the script tag before the first script in the HTML
    const scriptPosition = html.indexOf('<script');
    if (scriptPosition !== -1) {
      html = html.slice(0, scriptPosition) + scriptTag + html.slice(scriptPosition);
    } else {
      // If no script tag is found, insert before closing head tag
      const headClosePosition = html.indexOf('</head>');
      if (headClosePosition !== -1) {
        html = html.slice(0, headClosePosition) + scriptTag + html.slice(headClosePosition);
      }
    }
    
    return new Response(html, {
      headers: response.headers,
      status: response.status,
      statusText: response.statusText
    });
  }
  
  return response;
}
