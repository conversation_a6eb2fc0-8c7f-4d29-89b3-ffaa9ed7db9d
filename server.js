import { createServer } from 'http';
import { readFile } from 'fs/promises';
import { fileURLToPath } from 'url';
import path from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const PORT = process.env.PORT || 3000;

const server = createServer(async (req, res) => {
  try {
    // Get the file path from the URL
    let filePath = path.join(__dirname, 'dist', req.url === '/' ? 'index.html' : req.url);

    // If the path doesn't have an extension, serve index.html for client-side routing
    if (!path.extname(filePath)) {
      filePath = path.join(__dirname, 'dist', 'index.html');
    }

    // Read the file
    const data = await readFile(filePath).catch(() => {
      // If file not found, serve index.html
      return readFile(path.join(__dirname, 'dist', 'index.html'));
    });

    // Set content type based on file extension
    const ext = path.extname(filePath);
    let contentType = 'text/html';

    switch (ext) {
      case '.js':
        contentType = 'text/javascript';
        break;
      case '.css':
        contentType = 'text/css';
        break;
      case '.json':
        contentType = 'application/json';
        break;
      case '.png':
        contentType = 'image/png';
        break;
      case '.jpg':
        contentType = 'image/jpg';
        break;
    }

    // Send the response
    res.writeHead(200, { 'Content-Type': contentType });
    res.end(data);
  } catch (error) {
    console.error(error);
    res.writeHead(500);
    res.end('Server Error');
  }
});

server.listen(PORT, () => {
  console.log(`Server running on http://localhost:${PORT}`);
});
