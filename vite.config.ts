import { defineConfig } from "vite";
import react from "@vitejs/plugin-react-swc";
import path from "path";
import { componentTagger } from "lovable-tagger";
import fs from 'fs';
import { Plugin } from 'vite';

// Custom plugin to copy Cloudflare Pages files
function copyCloudflareFiles(): Plugin {
  return {
    name: 'copy-cloudflare-files',
    closeBundle() {
      // Copy _headers and _redirects to the dist folder
      if (fs.existsSync('_headers')) {
        fs.copyFileSync('_headers', 'dist/_headers');
        console.log('✅ Copied _headers to dist/');
      }
      if (fs.existsSync('_redirects')) {
        fs.copyFileSync('_redirects', 'dist/_redirects');
        console.log('✅ Copied _redirects to dist/');
      }
      if (fs.existsSync('_routes.json')) {
        fs.copyFileSync('_routes.json', 'dist/_routes.json');
        console.log('✅ Copied _routes.json to dist/');
      }
      if (fs.existsSync('test.html')) {
        fs.copyFileSync('test.html', 'dist/test.html');
        console.log('✅ Copied test.html to dist/');
      }
      if (fs.existsSync('fallback.html')) {
        fs.copyFileSync('fallback.html', 'dist/fallback.html');
        console.log('✅ Copied fallback.html to dist/');
      }
      if (fs.existsSync('debug.html')) {
        fs.copyFileSync('debug.html', 'dist/debug.html');
        console.log('✅ Copied debug.html to dist/');
      }
    }
  };
}

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => ({
  server: {
    host: "::",
    port: 8080,
  },
  plugins: [
    react(),
    mode === 'development' && componentTagger(),
    copyCloudflareFiles(),
  ].filter(Boolean),
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
}));
