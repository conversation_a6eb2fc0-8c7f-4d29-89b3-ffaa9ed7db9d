<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ChatNMatch Dating App</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
            color: #333;
        }
        h1 {
            color: #e91e63;
            text-align: center;
        }
        .card {
            border: 1px solid #eaeaea;
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .button {
            display: inline-block;
            background-color: #e91e63;
            color: white;
            padding: 10px 20px;
            border-radius: 5px;
            text-decoration: none;
            margin-top: 10px;
        }
        .center {
            text-align: center;
        }
    </style>
</head>
<body>
    <h1>ChatNMatch Dating App</h1>
    
    <div class="card">
        <h2>Welcome to ChatNMatch!</h2>
        <p>Find your perfect match and start meaningful conversations.</p>
        
        <div class="center">
            <a href="/auth" class="button">Sign In</a>
            <a href="/matches" class="button">Browse Matches</a>
        </div>
    </div>
    
    <div class="card">
        <h2>Features</h2>
        <ul>
            <li>Find matches based on your interests</li>
            <li>Chat with your matches in real-time</li>
            <li>Create a detailed profile to showcase your personality</li>
            <li>Secure and private communication</li>
        </ul>
    </div>
    
    <div class="card center">
        <p>© 2023 ChatNMatch. All rights reserved.</p>
    </div>
</body>
</html>
