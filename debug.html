<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ChatNMatch Debug</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        h1 {
            color: #e91e63;
        }
        .card {
            border: 1px solid #eaeaea;
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        pre {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
        }
        .success {
            color: green;
        }
        .error {
            color: red;
        }
    </style>
</head>
<body>
    <h1>ChatNMatch Debug Page</h1>
    
    <div class="card">
        <h2>Environment Variables</h2>
        <div id="env-vars">Loading...</div>
    </div>
    
    <div class="card">
        <h2>Supabase Connection Test</h2>
        <div id="supabase-test">Loading...</div>
    </div>

    <script src="/env-config.js"></script>
    <script>
        // Display environment variables (masked for security)
        document.addEventListener('DOMContentLoaded', () => {
            const envVarsDiv = document.getElementById('env-vars');
            const supabaseTestDiv = document.getElementById('supabase-test');
            
            // Check if environment variables are available
            setTimeout(() => {
                try {
                    const supabaseUrl = window.SUPABASE_URL || 'Not set';
                    const supabaseAnonKey = window.SUPABASE_ANON_KEY ? 
                        window.SUPABASE_ANON_KEY.substring(0, 5) + '...' + 
                        window.SUPABASE_ANON_KEY.substring(window.SUPABASE_ANON_KEY.length - 5) : 
                        'Not set';
                    
                    envVarsDiv.innerHTML = `
                        <pre>
SUPABASE_URL: ${supabaseUrl}
SUPABASE_ANON_KEY: ${supabaseAnonKey}
                        </pre>
                    `;
                    
                    // Test Supabase connection
                    if (window.SUPABASE_URL && window.SUPABASE_ANON_KEY) {
                        supabaseTestDiv.innerHTML = `<p>Testing connection to Supabase...</p>`;
                        
                        // Load Supabase JS from CDN
                        const script = document.createElement('script');
                        script.src = 'https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2';
                        script.onload = () => {
                            try {
                                const { createClient } = supabase;
                                const client = createClient(window.SUPABASE_URL, window.SUPABASE_ANON_KEY);
                                
                                // Test a simple query
                                client.from('profiles').select('count', { count: 'exact', head: true })
                                    .then(response => {
                                        if (response.error) {
                                            supabaseTestDiv.innerHTML = `
                                                <p class="error">❌ Connection failed:</p>
                                                <pre>${JSON.stringify(response.error, null, 2)}</pre>
                                            `;
                                        } else {
                                            supabaseTestDiv.innerHTML = `
                                                <p class="success">✅ Connection successful!</p>
                                                <pre>${JSON.stringify(response, null, 2)}</pre>
                                            `;
                                        }
                                    })
                                    .catch(error => {
                                        supabaseTestDiv.innerHTML = `
                                            <p class="error">❌ Connection error:</p>
                                            <pre>${error.message || JSON.stringify(error, null, 2)}</pre>
                                        `;
                                    });
                            } catch (error) {
                                supabaseTestDiv.innerHTML = `
                                    <p class="error">❌ Error initializing Supabase client:</p>
                                    <pre>${error.message || JSON.stringify(error, null, 2)}</pre>
                                `;
                            }
                        };
                        script.onerror = () => {
                            supabaseTestDiv.innerHTML = `
                                <p class="error">❌ Failed to load Supabase JS library</p>
                            `;
                        };
                        document.body.appendChild(script);
                    } else {
                        supabaseTestDiv.innerHTML = `
                            <p class="error">❌ Cannot test connection: Supabase credentials not available</p>
                        `;
                    }
                } catch (error) {
                    envVarsDiv.innerHTML = `
                        <p class="error">❌ Error:</p>
                        <pre>${error.message || JSON.stringify(error, null, 2)}</pre>
                    `;
                }
            }, 500); // Small delay to ensure env-config.js has loaded
        });
    </script>
</body>
</html>
