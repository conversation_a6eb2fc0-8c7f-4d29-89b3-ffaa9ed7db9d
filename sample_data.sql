-- Insert sample profiles
-- Note: These UUIDs are placeholders and would need to be replaced with actual user IDs from auth.users
INSERT INTO profiles (id, name, bio, gender, birth_date, location, avatar_url, interests, preferences)
VALUES
  ('00000000-0000-0000-0000-000000000001', '<PERSON>', 'I love hiking and photography', 'male', '1990-05-15', 'New York', 'https://randomuser.me/api/portraits/men/1.jpg', ARRAY['hiking', 'photography', 'travel'], '{"age_min": 25, "age_max": 35, "distance": 50, "gender_preference": "female"}'::jsonb),
  ('00000000-0000-0000-0000-000000000002', '<PERSON>', 'Coffee enthusiast and book lover', 'female', '1992-08-21', 'Los Angeles', 'https://randomuser.me/api/portraits/women/2.jpg', ARRAY['reading', 'coffee', 'yoga'], '{"age_min": 27, "age_max": 40, "distance": 30, "gender_preference": "male"}'::jsonb),
  ('00000000-0000-0000-0000-000000000003', '<PERSON>', 'Fitness fanatic and foodie', 'male', '1988-12-03', 'Chicago', 'https://randomuser.me/api/portraits/men/3.jpg', ARRAY['fitness', 'cooking', 'movies'], '{"age_min": 25, "age_max": 38, "distance": 40, "gender_preference": "female"}'::jsonb),
  ('00000000-0000-0000-0000-000000000004', 'Emily Davis', 'Art lover and dog person', 'female', '1994-03-17', 'San Francisco', 'https://randomuser.me/api/portraits/women/4.jpg', ARRAY['art', 'dogs', 'hiking'], '{"age_min": 26, "age_max": 36, "distance": 35, "gender_preference": "male"}'::jsonb),
  ('00000000-0000-0000-0000-000000000005', 'David Wilson', 'Tech geek and music lover', 'male', '1991-07-29', 'Seattle', 'https://randomuser.me/api/portraits/men/5.jpg', ARRAY['technology', 'music', 'gaming'], '{"age_min": 24, "age_max": 34, "distance": 45, "gender_preference": "female"}'::jsonb);

-- Insert sample likes
INSERT INTO likes (user_id, liked_user_id)
VALUES
  ('00000000-0000-0000-0000-000000000001', '00000000-0000-0000-0000-000000000002'),
  ('00000000-0000-0000-0000-000000000002', '00000000-0000-0000-0000-000000000001'),
  ('00000000-0000-0000-0000-000000000003', '00000000-0000-0000-0000-000000000004'),
  ('00000000-0000-0000-0000-000000000004', '00000000-0000-0000-0000-000000000003'),
  ('00000000-0000-0000-0000-000000000005', '00000000-0000-0000-0000-000000000002'),
  ('00000000-0000-0000-0000-000000000001', '00000000-0000-0000-0000-000000000004');

-- Insert sample chat rooms
INSERT INTO chat_rooms (id, participants)
VALUES
  ('00000000-0000-0000-0000-000000000001', ARRAY['00000000-0000-0000-0000-000000000001', '00000000-0000-0000-0000-000000000002']),
  ('00000000-0000-0000-0000-000000000002', ARRAY['00000000-0000-0000-0000-000000000003', '00000000-0000-0000-0000-000000000004']);

-- Insert sample messages
INSERT INTO messages (chat_room_id, sender_id, content, created_at)
VALUES
  ('00000000-0000-0000-0000-000000000001', '00000000-0000-0000-0000-000000000001', 'Hey Jane, how are you?', NOW() - INTERVAL '2 days'),
  ('00000000-0000-0000-0000-000000000001', '00000000-0000-0000-0000-000000000002', 'Hi John! I''m doing great, thanks for asking. How about you?', NOW() - INTERVAL '2 days' + INTERVAL '5 minutes'),
  ('00000000-0000-0000-0000-000000000001', '00000000-0000-0000-0000-000000000001', 'I''m good too. Would you like to grab coffee sometime?', NOW() - INTERVAL '2 days' + INTERVAL '10 minutes'),
  ('00000000-0000-0000-0000-000000000001', '00000000-0000-0000-0000-000000000002', 'That sounds lovely! How about this weekend?', NOW() - INTERVAL '2 days' + INTERVAL '15 minutes'),
  ('00000000-0000-0000-0000-000000000002', '00000000-0000-0000-0000-000000000003', 'Hello Emily, I noticed we both like hiking!', NOW() - INTERVAL '1 day'),
  ('00000000-0000-0000-0000-000000000002', '00000000-0000-0000-0000-000000000004', 'Hi Michael! Yes, I love hiking. Do you have a favorite trail?', NOW() - INTERVAL '1 day' + INTERVAL '30 minutes'),
  ('00000000-0000-0000-0000-000000000002', '00000000-0000-0000-0000-000000000003', 'I really enjoy the trails at Mount Rainier. Have you been there?', NOW() - INTERVAL '1 day' + INTERVAL '45 minutes'),
  ('00000000-0000-0000-0000-000000000002', '00000000-0000-0000-0000-000000000004', 'Not yet, but it''s on my bucket list! Would love to check it out sometime.', NOW() - INTERVAL '1 day' + INTERVAL '1 hour');
